import numpy as np
from scipy.signal import butter, filtfilt, medfilt
from scipy.ndimage import uniform_filter1d
import warnings
from dataclasses import dataclass, field

@dataclass
class PreprocessingConfig:
    """Configuration for the Marine IMU Preprocessor."""
    # Master switch
    apply_preprocessing: bool = True

    # Stage 1: Spike Removal (Median Filter)
    apply_spike_removal: bool = True
    spike_kernel_size: int = 5

    # Stage 2: Low-Pass Filter (Butterworth)
    apply_low_pass: bool = True
    low_pass_cutoff_hz: float = 8.0

    # Stage 3: Smoothing (Moving Average) - Gyroscope only
    apply_smoothing: bool = True
    smoothing_window_size: int = 5

class MarineIMUPreprocessor:
    """
    Multi-stage IMU preprocessing filter designed for marine applications.
    Now accepts a configuration object to control each stage.

    Handles common marine IMU noise sources:
    - Engine vibrations (high-frequency)
    - Wave motion (low-frequency)
    - Sensor spikes and outliers
    - Electromagnetic interference
    """

    def __init__(self, sampling_rate: float, config: PreprocessingConfig):
        """
        Initialize the preprocessor.

        Args:
            sampling_rate: IMU sampling rate in Hz.
            config: Configuration object for the filters.
        """
        self.sampling_rate = sampling_rate
        self.config = config
        self.nyquist = sampling_rate / 2

        # Design filter coefficients based on config
        self._design_filters()
    
    def _design_filters(self):
        """Design filter coefficients based on configuration."""
        if self.config.apply_low_pass and self.config.low_pass_cutoff_hz < self.nyquist:
            self.lp_b, self.lp_a = butter(
                N=4,
                Wn=self.config.low_pass_cutoff_hz / self.nyquist,
                btype='low',
                analog=False
            )
        else:
            self.lp_b, self.lp_a = None, None
    
    def preprocess_gyroscope(self, gyro_data):
        """Preprocess gyroscope data based on the configuration."""
        if not self.config.apply_preprocessing:
            return gyro_data

        if gyro_data.shape[1] != 3:
            raise ValueError("Gyroscope data must be Nx3 array")

        filtered_data = gyro_data.copy()

        if self.config.apply_spike_removal:
            print("Stage 1: Removing spikes...")
            filtered_data = self._remove_spikes(filtered_data, kernel_size=self.config.spike_kernel_size)

        if self.config.apply_low_pass and self.lp_b is not None:
            print("Stage 2: Applying low-pass filter...")
            filtered_data = self._apply_lowpass(filtered_data)

        if self.config.apply_smoothing:
            print("Stage 3: Applying smoothing...")
            filtered_data = self._apply_smoothing(filtered_data, window_size=self.config.smoothing_window_size)

        return filtered_data
    
    def preprocess_accelerometer(self, accel_data):
        """Preprocess accelerometer data based on the configuration."""
        if not self.config.apply_preprocessing:
            return accel_data

        if accel_data.shape[1] != 3:
            raise ValueError("Accelerometer data must be Nx3 array")

        filtered_data = accel_data.copy()

        if self.config.apply_spike_removal:
            # Use a smaller kernel for accelerometer to preserve gravity
            accel_kernel = max(3, self.config.spike_kernel_size - 2)
            print("Stage 1: Removing accelerometer spikes...")
            filtered_data = self._remove_spikes(filtered_data, kernel_size=accel_kernel)

        if self.config.apply_low_pass and self.lp_b is not None:
            print("Stage 2: Applying low-pass filter...")
            filtered_data = self._apply_lowpass(filtered_data)

        return filtered_data
    
    def _remove_spikes(self, data, kernel_size=5):
        """Remove spikes using adaptive median filter"""
        filtered_data = data.copy()
        
        for i in range(data.shape[1]):
            # Apply median filter
            median_filtered = medfilt(data[:, i], kernel_size=kernel_size)
            
            # Calculate residuals
            residuals = np.abs(data[:, i] - median_filtered)
            threshold = np.percentile(residuals, 95)  # Adaptive threshold
            
            # Replace spikes with median values
            spike_mask = residuals > threshold * 3
            filtered_data[spike_mask, i] = median_filtered[spike_mask]
        
        return filtered_data
    
    def _apply_lowpass(self, data):
        """Apply low-pass Butterworth filter"""
        filtered_data = data.copy()
        
        for i in range(data.shape[1]):
            # Use zero-phase filtering (filtfilt) to avoid phase delay
            filtered_data[:, i] = filtfilt(self.lp_b, self.lp_a, data[:, i])
        
        return filtered_data
    
    def _apply_smoothing(self, data, window_size=5):
        """Apply additional smoothing using moving average"""
        filtered_data = data.copy()
        
        for i in range(data.shape[1]):
            # Apply uniform filter (moving average)
            filtered_data[:, i] = uniform_filter1d(
                data[:, i], 
                size=window_size, 
                mode='nearest'
            )
        
        return filtered_data
    
    def analyze_noise_characteristics(self, data):
        """
        Analyze noise characteristics of IMU data
        
        Returns:
            Dictionary with noise statistics
        """
        stats = {}
        
        for i, axis in enumerate(['X', 'Y', 'Z']):
            # Calculate noise metrics
            diff = np.diff(data[:, i])
            stats[f'{axis}_std'] = np.std(data[:, i])
            stats[f'{axis}_diff_std'] = np.std(diff)
            stats[f'{axis}_max_abs'] = np.max(np.abs(data[:, i]))
            stats[f'{axis}_spike_count'] = np.sum(np.abs(diff) > 5 * np.std(diff))
        
        return stats
    
    def estimate_optimal_cutoff(self, gyro_data, method='spectral'):
        """
        Estimate optimal cutoff frequency based on data characteristics
        
        Args:
            gyro_data: Gyroscope data for analysis
            method: 'spectral' or 'statistical'
            
        Returns:
            Recommended cutoff frequency
        """
        if method == 'spectral':
            # Use FFT to find dominant frequencies
            from scipy.fft import fft, fftfreq
            
            # Analyze frequency content
            freqs = fftfreq(len(gyro_data), 1/self.sampling_rate)
            
            # Find frequency where 95% of power is contained
            power_sum = 0
            total_power = 0
            
            for i in range(gyro_data.shape[1]):
                fft_data = np.abs(fft(gyro_data[:, i]))
                total_power += np.sum(fft_data[:len(fft_data)//2])
            
            # Find 95% power cutoff
            cutoff_freq = min(20.0, self.boat_dynamics_cutoff * 1.5)
            
        else:  # Statistical method
            # Use statistical properties to estimate cutoff
            noise_stats = self.analyze_noise_characteristics(gyro_data)
            avg_noise = np.mean([noise_stats[f'{axis}_diff_std'] for axis in ['X', 'Y', 'Z']])
            
            # Higher noise suggests need for lower cutoff
            if avg_noise > 0.1:
                cutoff_freq = 5.0
            elif avg_noise > 0.05:
                cutoff_freq = 8.0
            else:
                cutoff_freq = 12.0
        
        return cutoff_freq

# Example usage for your marine survey data
def process_marine_imu_data(gyro_data, accel_data, sampling_rate=100.0):
    """
    Process marine IMU data with recommended settings
    
    Args:
        gyro_data: Nx3 gyroscope data
        accel_data: Nx3 accelerometer data
        sampling_rate: IMU sampling rate
        
    Returns:
        Tuple of (filtered_gyro, filtered_accel)
    """
    
    # Initialize preprocessor
    preprocessor = MarineIMUPreprocessor(
        sampling_rate=sampling_rate,
        boat_dynamics_cutoff=8.0  # Good for boat dynamics
    )
    
    # Analyze data characteristics
    print("Analyzing gyroscope noise characteristics...")
    gyro_stats = preprocessor.analyze_noise_characteristics(gyro_data)
    print(f"Gyro noise stats: {gyro_stats}")
    
    # Estimate optimal cutoff
    optimal_cutoff = preprocessor.estimate_optimal_cutoff(gyro_data)
    print(f"Estimated optimal cutoff: {optimal_cutoff:.1f} Hz")
    
    # Process gyroscope data
    filtered_gyro = preprocessor.preprocess_gyroscope(
        gyro_data, 
        aggressive_denoising=True
    )
    
    # Process accelerometer data
    filtered_accel = preprocessor.preprocess_accelerometer(
        accel_data, 
        preserve_gravity=True
    )
    
    return filtered_gyro, filtered_accel

# Integration with your fusion system
def integrate_with_fusion_engine(fusion_engine, filtered_gyro, filtered_accel, 
                               other_sensors, sensor_config):
    """
    Integrate filtered IMU data with your existing fusion engine
    
    Args:
        fusion_engine: Your FusionEngine instance
        filtered_gyro: Preprocessed gyroscope data
        filtered_accel: Preprocessed accelerometer data
        other_sensors: Other sensor data (GPS, magnetometer, etc.)
        sensor_config: Sensor configuration
    """
    
    # Create modified sensor measurements with filtered IMU data
    from copy import deepcopy
    
    filtered_measurements = deepcopy(other_sensors)
    filtered_measurements.gyroscope = filtered_gyro
    filtered_measurements.accelerometer = filtered_accel
    
    # Reduce noise parameters in config since data is pre-filtered
    filtered_sensor_config = deepcopy(sensor_config)
    filtered_sensor_config.gyroscope_noise *= 0.7  # Reduce noise estimate
    filtered_sensor_config.accelerometer_noise *= 0.8
    
    return filtered_measurements, filtered_sensor_config