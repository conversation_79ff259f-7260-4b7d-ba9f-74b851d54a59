# Future Tasks - Platform Maintenance and Improvements

This document outlines maintenance issues and improvement tasks identified in the INS/GPS Testing Platform codebase. These issues should be addressed to improve code maintainability, readability, and robustness.

## ✅ **Recently Completed Tasks**

### **Pohang Integration with 16-State Fusion Module** *(Completed)*
- [x] Updated import paths in pohang_integration.py
- [x] Added 16-state specific configuration parameters
- [x] Updated sensor configuration templates
- [x] Created compatibility test script
- [x] Verified sensor data compatibility (accelerometer, gyroscope, GPS position only)

## 🚨 High Priority Issues

### 1. **Magic Numbers and Hardcoded Values**

**Problem**: Numerous magic numbers and hardcoded values throughout the codebase make it difficult to understand and maintain.

**Locations**:
- `fusion/saeid_gps_ins_ekf.py`: Lines 269, 322, 250, 578
- `fusion/fusion_engine.py`: Line 262 (tolerance = 1e-6)
- `utils/constants.py`: Line 95 (Boston-specific magnetic field)
- `simulation/simulation_engine.py`: Various numerical thresholds

**Examples**:
```python
# Bad: Magic numbers without explanation
if omega_norm < 1e-8:  # What does this threshold represent?
tolerance = 1e-6  # Why this specific value?
DEFAULT_MAGNETIC_FIELD_NED = [20.5, -4.2, 51.8]  # Boston area hardcoded

# Good: Named constants with documentation
OMEGA_NORM_THRESHOLD = 1e-8  # Minimum angular velocity for rotation detection
TIME_TOLERANCE_SECONDS = 1e-6  # Numerical precision for time matching
```

**Tasks**:
- [x] Extract hardcoded Earth model constants (EARTH_RADIUS, EARTH_FLATTENING, EARTH_ROTATION_RATE, STANDARD_GRAVITY)
- [x] Replace hardcoded magnetic field values with DEFAULT_MAGNETIC_FIELD_NED constant
- [ ] Extract remaining magic numbers to named constants
- [ ] Add documentation explaining threshold choices
- [ ] Move location-specific values to configuration files
- [ ] Create constants.py sections for different modules

### 2. **Duplicate Code in Setup Files**

**Problem**: Identical code exists in multiple setup files, creating maintenance burden.

**Locations**:
- `setup_environment.py` (lines 58-66)
- `z_setup/setup_environment.py` (lines 59-67)

**Tasks**:
- [ ] Consolidate setup functionality into single module
- [ ] Remove duplicate setup_environment.py from root directory
- [ ] Update installation instructions to point to z_setup/ only
- [ ] Create shared configuration for package requirements

### 3. **Complex Functions with Multiple Responsibilities**

**Problem**: Large functions doing too many things, violating Single Responsibility Principle.

**Locations**:
- `gui/pohang_results_tab.py::update_trajectory_plots()` (200+ lines)
- `gui/pohang_results_tab.py::_convert_to_local_coordinates()` (100+ lines)
- `simulation/simulation_engine.py::run_simulation()` (complex orchestration)

**Tasks**:
- [ ] Break down `update_trajectory_plots()` into smaller functions:
  - `_load_trajectory_data()`
  - `_plot_2d_trajectory()`
  - `_plot_error_analysis()`
  - `_update_statistics_display()`
- [ ] Extract coordinate conversion logic to utility classes
- [ ] Separate data loading from visualization logic

### 4. **Inconsistent Error Handling**

**Problem**: Mixed error handling patterns make debugging difficult.

**Locations**:
- `fusion/saeid_gps_ins_ekf.py`: Some try/catch, some silent returns
- `gui/` modules: Inconsistent error reporting
- `pohang/` modules: Mixed error handling approaches

**Tasks**:
- [ ] Standardize error handling patterns across all modules
- [ ] Add comprehensive logging for all error conditions
- [ ] Create custom exception classes for domain-specific errors
- [ ] Implement consistent error reporting in GUI components

## 🔧 Medium Priority Issues

### 5. **Naming Inconsistencies and Typos**

**Problem**: Inconsistent naming conventions and typos reduce code professionalism.

**Issues Found**:
- `pohang_analizor.py` → should be `pohang_analyzer.py`
- `cobfig.json` → should be `config.json`
- Mixed naming conventions (snake_case, camelCase, UPPER_CASE)

**Tasks**:
- [ ] Fix typos in file names:
  - Rename `pohang_analizor.py` to `pohang_analyzer.py`
  - Rename `cobfig.json` to `config.json`
- [ ] Standardize variable naming to snake_case throughout
- [ ] Update all imports and references after renaming
- [ ] Create naming convention guidelines document

### 6. **Poor Separation of Concerns**

**Problem**: GUI logic mixed with business logic, making testing and reuse difficult.

**Locations**:
- `gui/pohang_tab.py::run_processing()`: GUI and business logic mixed
- `gui/estimation_tab.py`: Similar mixing of concerns
- `gui/visualization_tab.py`: Plotting logic mixed with data processing

**Tasks**:
- [ ] Extract business logic from GUI components
- [ ] Create service layer for data processing operations
- [ ] Implement proper MVC/MVP pattern in GUI components
- [ ] Make business logic testable independently of GUI

### 7. **Complex State Management in EKF**

**Problem**: State vector management using slice indices is error-prone and unclear.

**Location**: `fusion/saeid_gps_ins_ekf.py`

**Current Issues**:
```python
# Cryptic slice-based state management
self.QUAT_IDX = slice(0, 4)
self.OMEGA_IDX = slice(4, 7)
F[0:4, 4:7] = self._quaternion_omega_jacobian(q, omega, dt)  # What does this do?
```

**Tasks**:
- [ ] Create StateVector class with named properties
- [ ] Add clear documentation for state vector layout
- [ ] Implement bounds checking for state access
- [ ] Create helper methods for state vector operations

### 8. **Coordinate System Complexity**

**Problem**: Complex coordinate transformations scattered throughout GUI code.

**Location**: `gui/pohang_results_tab.py::_convert_to_local_coordinates()`

**Tasks**:
- [ ] Create dedicated CoordinateTransform utility class
- [ ] Move all coordinate conversion logic out of GUI
- [ ] Add comprehensive testing for coordinate transformations
- [ ] Document coordinate system conventions clearly

## 📚 Low Priority Improvements

### 9. **Configuration Validation**

**Tasks**:
- [ ] Add JSON schema validation for configuration files
- [ ] Implement runtime configuration validation
- [ ] Create configuration migration tools for version updates
- [ ] Add configuration validation in GUI

### 10. **Testing Infrastructure**

**Tasks**:
- [ ] Add unit tests for complex mathematical functions
- [ ] Create integration tests for fusion pipeline
- [ ] Add GUI testing framework
- [ ] Implement continuous integration testing

### 11. **Documentation Improvements**

**Tasks**:
- [ ] Add inline documentation for complex algorithms
- [ ] Create developer documentation for architecture
- [ ] Document coordinate system conventions
- [ ] Add troubleshooting guides

### 12. **Performance Optimizations**

**Tasks**:
- [ ] Profile memory usage in long simulations
- [ ] Optimize coordinate transformation calculations
- [ ] Implement caching for repeated calculations
- [ ] Add progress reporting for long operations

## 📋 Implementation Strategy

### Phase 1: Critical Fixes (Week 1-2)
1. Fix naming typos and inconsistencies
2. Remove duplicate setup code
3. Extract magic numbers to constants
4. Standardize error handling patterns

### Phase 2: Structural Improvements (Week 3-4)
1. Break down complex functions
2. Separate GUI from business logic
3. Create coordinate transformation utilities
4. Improve state management in EKF

### Phase 3: Quality Improvements (Week 5-6)
1. Add comprehensive testing
2. Improve documentation
3. Add configuration validation
4. Performance optimizations

## 🎯 Success Metrics

- [ ] All magic numbers replaced with named constants
- [ ] No functions longer than 50 lines
- [ ] Consistent error handling across all modules
- [ ] 100% separation of GUI and business logic
- [ ] All coordinate transformations in utility classes
- [ ] Zero naming inconsistencies or typos

---

**Note**: This document should be updated as tasks are completed and new issues are discovered. Each task should be tracked individually with proper issue management.
