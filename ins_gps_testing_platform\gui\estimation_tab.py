"""Estimation Tab - Load Data and Run INS/GPS Fusion Independently"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
import numpy as np
from pathlib import Path
from utils.helpers import DataPersistenceManager
from config.configuration_manager import TrajectoryConfig, SensorConfig, EnvironmentConfig
from simulation.simulation_engine import GroundTruth, SensorMeasurements, SimulationData
from fusion.ekf_28.fusion_engine import FusionEngine

logger = logging.getLogger(__name__)

class EstimationTab:
    """Estimation tab for loading data and running fusion independently"""
    
    def __init__(self, parent, main_gui):
        self.parent = parent
        self.main_gui = main_gui
        
        # Initialize components
        self.data_manager = DataPersistenceManager()
        self.fusion_engine = FusionEngine()
        
        # Data storage
        self.loaded_simulation_data = None
        self.loaded_config = None
        self.estimation_results = None
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Create UI components
        self.create_status_section()  # Create status section first
        self.create_data_loading_section()
        self.create_estimation_controls()

        # Initialize scenario list after all UI components are created
        self.refresh_scenarios()

        logger.info("Estimation tab initialized")
    
    def create_data_loading_section(self):
        """Create data loading section"""
        # Data loading frame
        data_frame = ttk.LabelFrame(self.frame, text="Data Loading")
        data_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Scenario selection
        scenario_frame = ttk.Frame(data_frame)
        scenario_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(scenario_frame, text="Available Scenarios:").pack(side=tk.LEFT)
        
        self.scenario_var = tk.StringVar()
        self.scenario_combo = ttk.Combobox(scenario_frame, textvariable=self.scenario_var,
                                          state="readonly", width=30)
        self.scenario_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(scenario_frame, text="Refresh", 
                  command=self.refresh_scenarios).pack(side=tk.LEFT, padx=5)
        ttk.Button(scenario_frame, text="Load Scenario", 
                  command=self.load_scenario).pack(side=tk.LEFT, padx=5)
        
        # Manual file loading
        manual_frame = ttk.Frame(data_frame)
        manual_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(manual_frame, text="Or load files manually:").pack(side=tk.LEFT)
        ttk.Button(manual_frame, text="Load Data File", 
                  command=self.load_data_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(manual_frame, text="Load Config File", 
                  command=self.load_config_file).pack(side=tk.LEFT, padx=5)
        

    
    def create_estimation_controls(self):
        """Create estimation control section"""
        # Estimation frame
        est_frame = ttk.LabelFrame(self.frame, text="Estimation Controls")
        est_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Control buttons
        button_frame = ttk.Frame(est_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="Run Estimation", 
                  command=self.run_estimation).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Show Results", 
                  command=self.show_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear Data", 
                  command=self.clear_data).pack(side=tk.LEFT, padx=5)
    
    def create_status_section(self):
        """Create status display section"""
        # Status frame
        status_frame = ttk.LabelFrame(self.frame, text="Status")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Status text widget
        self.status_text = tk.Text(status_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Initial status
        self.update_status("Ready to load simulation data and configuration.")
    
    def refresh_scenarios(self):
        """Refresh the list of available scenarios"""
        try:
            scenarios = self.data_manager.list_scenarios()
            self.scenario_combo['values'] = scenarios
            if scenarios:
                self.scenario_combo.set(scenarios[0])
            self.update_status(f"Found {len(scenarios)} available scenarios.")
        except Exception as e:
            logger.error(f"Failed to refresh scenarios: {e}")
            self.update_status(f"Error refreshing scenarios: {str(e)}")
    
    def load_scenario(self):
        """Load selected scenario data and configuration"""
        scenario_name = self.scenario_var.get()
        if not scenario_name:
            messagebox.showwarning("No Selection", "Please select a scenario to load.")
            return
        
        try:
            # Get scenario files
            files = self.data_manager.get_scenario_files(scenario_name)
            
            if files["data_file"] is None or files["config_file"] is None:
                messagebox.showerror("Missing Files", 
                                   "Scenario is missing data or configuration files.")
                return
            
            # Load data and configuration
            self.load_data_from_file(files["data_file"])
            self.load_config_from_file(files["config_file"])
            
            self.update_status(f"Successfully loaded scenario: {scenario_name}")
            
        except Exception as e:
            logger.error(f"Failed to load scenario: {e}")
            messagebox.showerror("Load Error", f"Failed to load scenario:\n{str(e)}")
            self.update_status(f"Error loading scenario: {str(e)}")
    
    def load_data_file(self):
        """Load simulation data from file"""
        filename = filedialog.askopenfilename(
            title="Load Simulation Data",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            defaultextension=".json"
        )
        if filename:
            self.load_data_from_file(Path(filename))
    
    def load_config_file(self):
        """Load configuration from file"""
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            defaultextension=".json"
        )
        if filename:
            self.load_config_from_file(Path(filename))
    
    def load_data_from_file(self, filepath: Path):
        """Load simulation data from specified file"""
        try:
            data_dict = self.data_manager.load_simulation_data(filepath)
            if data_dict is None:
                raise ValueError("Failed to load simulation data")
            
            # Reconstruct data objects
            ground_truth = GroundTruth(
                time=np.array(data_dict["ground_truth"]["time"]),
                position=np.array(data_dict["ground_truth"]["position"]),
                velocity=np.array(data_dict["ground_truth"]["velocity"]),
                acceleration=np.array(data_dict["ground_truth"]["acceleration"]),
                orientation=np.array(data_dict["ground_truth"]["orientation"]),
                angular_velocity=np.array(data_dict["ground_truth"]["angular_velocity"])
            )
            
            # Convert time dictionary back to proper format
            time_dict = {}
            for sensor_name, time_array in data_dict["sensor_measurements"]["time"].items():
                time_dict[sensor_name] = np.array(time_array)
            
            sensor_measurements = SensorMeasurements(
                time=time_dict,
                accelerometer=np.array(data_dict["sensor_measurements"]["accelerometer"]),
                gyroscope=np.array(data_dict["sensor_measurements"]["gyroscope"]),
                magnetometer=np.array(data_dict["sensor_measurements"]["magnetometer"]),
                gps_position=np.array(data_dict["sensor_measurements"]["gps_position"]),
                gps_velocity=np.array(data_dict["sensor_measurements"]["gps_velocity"])
            )
            
            # Store loaded data (configuration will be loaded separately)
            self.loaded_simulation_data = SimulationData(
                ground_truth=ground_truth,
                sensor_measurements=sensor_measurements,
                configuration={}  # Will be populated when config is loaded
            )
            
            self.update_status(f"Simulation data loaded from: {filepath.name}")
            
        except Exception as e:
            logger.error(f"Failed to load data file: {e}")
            messagebox.showerror("Load Error", f"Failed to load data file:\n{str(e)}")
            self.update_status(f"Error loading data: {str(e)}")
    
    def load_config_from_file(self, filepath: Path):
        """Load configuration from specified file"""
        try:
            config_dict = self.data_manager.load_configuration(filepath)
            if config_dict is None:
                raise ValueError("Failed to load configuration")
            
            # Reconstruct configuration objects
            self.loaded_config = {
                'trajectory': TrajectoryConfig(**config_dict['trajectory']),
                'sensors': SensorConfig(**config_dict['sensors']),
                'environment': EnvironmentConfig(**config_dict['environment'])
            }
            
            # Update simulation data configuration if data is loaded
            if self.loaded_simulation_data is not None:
                self.loaded_simulation_data.configuration = self.loaded_config
            
            self.update_status(f"Configuration loaded from: {filepath.name}")
            
        except Exception as e:
            logger.error(f"Failed to load config file: {e}")
            messagebox.showerror("Load Error", f"Failed to load config file:\n{str(e)}")
            self.update_status(f"Error loading config: {str(e)}")
    
    def run_estimation(self):
        """Run INS/GPS fusion estimation"""
        if self.loaded_simulation_data is None:
            messagebox.showwarning("No Data", "Please load simulation data first.")
            return
        
        if self.loaded_config is None:
            messagebox.showwarning("No Config", "Please load configuration first.")
            return
        
        try:
            self.update_status("Starting estimation...")
            self.main_gui.update_progress(0)
            
            # Run fusion with separate configuration
            self.main_gui.update_progress(50)
            self.estimation_results = self.fusion_engine.run_fusion(
                self.loaded_simulation_data,
                self.loaded_config['sensors'],
                self.loaded_config['trajectory'],
                self.loaded_config['environment']
            )
            
            self.main_gui.update_progress(100)
            self.update_status("Estimation completed successfully!")
            
            messagebox.showinfo("Success", "Estimation completed successfully!")
            
        except Exception as e:
            logger.error(f"Estimation failed: {e}")
            messagebox.showerror("Estimation Error", f"Estimation failed:\n{str(e)}")
            self.update_status(f"Estimation error: {str(e)}")
            self.main_gui.update_progress(0)
    
    def show_results(self):
        """Show estimation results in visualization tab"""
        if self.estimation_results is None:
            messagebox.showwarning("No Results", "No estimation results available. Run estimation first.")
            return
        
        if self.loaded_simulation_data is None:
            messagebox.showwarning("No Data", "No simulation data available for comparison.")
            return
        
        try:
            # Run analysis to prepare results for visualization
            analysis_results = self.main_gui.analysis_engine.run_analysis(
                self.loaded_simulation_data, self.estimation_results
            )
            
            # Update visualization tab
            self.main_gui.viz_tab.update_results(analysis_results)
            
            # Switch to visualization tab
            self.main_gui.notebook.select(2)  # Visualization is tab 2 (0=Simulation, 1=Estimation, 2=Visualization)
            
            self.update_status("Results displayed in visualization tab.")
            
        except Exception as e:
            logger.error(f"Failed to show results: {e}")
            messagebox.showerror("Display Error", f"Failed to show results:\n{str(e)}")
            self.update_status(f"Error displaying results: {str(e)}")
    
    def clear_data(self):
        """Clear all loaded data"""
        self.loaded_simulation_data = None
        self.loaded_config = None
        self.estimation_results = None
        self.update_status("All data cleared.")
    
    def update_status(self, message: str):
        """Update status display"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.main_gui.root.update_idletasks()
