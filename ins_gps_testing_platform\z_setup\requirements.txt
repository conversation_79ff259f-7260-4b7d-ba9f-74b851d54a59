# INS/GPS Testing Platform Dependencies

# Core scientific computing and numerical libraries
numpy>=1.21.0
scipy>=1.7.0

# Data visualization and plotting
matplotlib>=3.5.0
plotly>=5.0.0

# Data handling and file I/O
pandas>=1.3.0
h5py>=3.6.0

# GUI enhancements
# tkinter-tooltip>=2.1.0  

# Note: tkinter is included with Python standard library
# Note: pathlib is included with Python 3.4+
# Note: json, os, sys, logging, copy, dataclasses, typing are all standard library modules
