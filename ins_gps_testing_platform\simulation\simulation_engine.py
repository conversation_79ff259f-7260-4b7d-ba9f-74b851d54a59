"""Simulation Engine - Trajectory and Sensor Simulation"""
import numpy as np
from scipy.spatial.transform import Rotation
import logging
from dataclasses import dataclass
from typing import Dict, Any, Tuple
from config.configuration_manager import TrajectoryConfig, SensorConfig, EnvironmentConfig
from utils.constants import STANDARD_GRAVITY

logger = logging.getLogger(__name__)

@dataclass
class GroundTruth:
    """Ground truth trajectory data"""
    time: np.ndarray
    position: np.ndarray  # [N, 3] NED coordinates
    velocity: np.ndarray  # [N, 3] NED velocities
    acceleration: np.ndarray  # [N, 3] NED accelerations
    orientation: np.ndarray  # [N, 4] quaternions [w, x, y, z]
    angular_velocity: np.ndarray  # [N, 3] body frame angular velocities

@dataclass
class SensorMeasurements:
    """Complete sensor measurement data"""
    time: Dict[str, np.ndarray]  # Time vectors for each sensor
    accelerometer: np.ndarray    # [N, 3] body frame
    gyroscope: np.ndarray       # [N, 3] body frame
    magnetometer: np.ndarray    # [N, 3] body frame
    gps_position: np.ndarray    # [N, 3] LLA format
    gps_velocity: np.ndarray    # [N, 3] NED frame

@dataclass
class SimulationData:
    """Complete simulation dataset"""
    ground_truth: GroundTruth
    sensor_measurements: SensorMeasurements
    configuration: Dict[str, Any]

class TrajectoryGenerator:
    """Generates ground truth trajectories"""
    
    def __init__(self):
        pass
    
    def generate_trajectory(self, config: TrajectoryConfig) -> GroundTruth:
        """Generate ground truth trajectory based on configuration"""
        
        # Create time vector
        dt = 1.0 / config.sampling_rate
        time = np.arange(0, config.duration, dt)
        n_points = len(time)
        
        # Generate trajectory based on type
        if config.trajectory_type == "CIRCULAR":
            return self._generate_circular(time, config)
        elif config.trajectory_type == "FIGURE8":
            return self._generate_figure8(time, config)
        elif config.trajectory_type == "SQUARE":
            return self._generate_square(time, config)
        elif config.trajectory_type == "STRAIGHT":
            return self._generate_straight(time, config)
        elif config.trajectory_type == "SPIRAL":
            return self._generate_spiral(time, config)
        elif config.trajectory_type == "SURVEY_LINES":
            return self._generate_survey_lines(time, config)
        elif config.trajectory_type == "COASTAL_PATROL":
            return self._generate_coastal_patrol(time, config)
        else:
            logger.warning(f"Unknown trajectory type: {config.trajectory_type}")
            return self._generate_circular(time, config)
    
    def _generate_circular(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate circular trajectory"""
        n_points = len(time)
        
        # Angular velocity for constant speed
        omega = config.vessel_speed / config.radius
        
        # Positions in local NED frame
        angles = omega * time
        north = config.radius * np.cos(angles)
        east = config.radius * np.sin(angles)
        down = np.zeros(n_points)  # Constant altitude
        
        position = np.column_stack([north, east, down])
        
        # Velocities
        vel_north = -config.radius * omega * np.sin(angles)
        vel_east = config.radius * omega * np.cos(angles)
        vel_down = np.zeros(n_points)
        
        velocity = np.column_stack([vel_north, vel_east, vel_down])
        
        # Accelerations (centripetal)
        acc_north = -config.radius * omega**2 * np.cos(angles)
        acc_east = -config.radius * omega**2 * np.sin(angles)
        acc_down = np.zeros(n_points)
        
        acceleration = np.column_stack([acc_north, acc_east, acc_down])
        
        # Orientation (heading aligned with velocity)
        headings = np.arctan2(vel_east, vel_north)
        orientation = np.zeros((n_points, 4))
        for i, heading in enumerate(headings):
            r = Rotation.from_euler('z', heading, degrees=False)
            quat = r.as_quat()  # [x, y, z, w]
            orientation[i] = [quat[3], quat[0], quat[1], quat[2]]  # [w, x, y, z]
        
        # Angular velocity (constant turn rate)
        angular_velocity = np.zeros((n_points, 3))
        angular_velocity[:, 2] = omega  # Yaw rate
        
        return GroundTruth(time, position, velocity, acceleration, 
                          orientation, angular_velocity)
    
    def _generate_figure8(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate figure-8 trajectory"""
        n_points = len(time)
        
        # Parametric figure-8 (Lemniscate)
        # Period for complete figure-8
        T = 2 * np.pi * config.figure8_width / config.vessel_speed
        t_param = 2 * np.pi * time / T
        
        # Lemniscate equations
        scale_x = config.figure8_width / 2
        scale_y = config.figure8_height / 2
        
        # Parametric equations
        cos_t = np.cos(t_param)
        sin_t = np.sin(t_param)
        denominator = 1 + sin_t**2
        
        east = scale_x * cos_t / denominator
        north = scale_y * sin_t * cos_t / denominator
        down = np.zeros(n_points)
        
        position = np.column_stack([north, east, down])
        
        # Numerical derivatives for velocity and acceleration
        dt = time[1] - time[0]
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)
        
        # Orientation from velocity direction
        orientation = self._compute_orientation_from_velocity(velocity)
        
        # Angular velocity from orientation changes
        angular_velocity = self._compute_angular_velocity(orientation, dt)
        
        return GroundTruth(time, position, velocity, acceleration, 
                          orientation, angular_velocity)
    
    def _generate_square(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate square trajectory with smooth corners"""
        # Square parameters
        side_length = config.square_size
        speed = config.vessel_speed
        vessel_length = config.vessel_length
        turn_radius_factor = config.min_turn_radius_factor

        # Calculate minimum turn radius
        min_turn_radius = vessel_length * turn_radius_factor

        # Time step
        dt = time[1] - time[0] if len(time) > 1 else 0.01

        # Square vertices (NED frame) - corners of the square
        vertices = np.array([
            [side_length/2, -side_length/2, 0],    # Top-left
            [side_length/2, side_length/2, 0],     # Top-right
            [-side_length/2, side_length/2, 0],    # Bottom-right
            [-side_length/2, -side_length/2, 0]    # Bottom-left
        ])

        # Headings for each side (in radians, 0 = North)
        side_headings = [
            np.pi/2,    # East (first side: top-left to top-right)
            np.pi,      # South (second side: top-right to bottom-right)
            -np.pi/2,   # West (third side: bottom-right to bottom-left)
            0.0         # North (fourth side: bottom-left to top-left)
        ]

        # Generate smooth trajectory through vertices
        all_positions = []
        all_velocities = []
        all_headings = []

        for i in range(4):  # Four sides of the square
            start_vertex = vertices[i]
            end_vertex = vertices[(i + 1) % 4]
            start_heading = side_headings[i]
            end_heading = side_headings[(i + 1) % 4]

            # Create straight segment for the side
            side_positions, side_velocities, side_headings_arr = self._create_straight_segment(
                start_vertex, end_vertex, speed, dt
            )

            all_positions.append(side_positions)
            all_velocities.append(side_velocities)
            all_headings.append(side_headings_arr)

            # Create smooth turn at the corner (except for the last corner)
            if i < 3:  # Don't add turn after the last side
                next_vertex = vertices[(i + 2) % 4]
                turn_positions, turn_velocities, turn_headings_arr = self._create_smooth_turn(
                    end_vertex, end_heading, next_vertex, side_headings[(i + 1) % 4],
                    min_turn_radius, speed, dt
                )

                all_positions.append(turn_positions)
                all_velocities.append(turn_velocities)
                all_headings.append(turn_headings_arr)

        # Concatenate all segments
        if all_positions:
            position = np.vstack(all_positions)
            velocity = np.vstack(all_velocities)
            headings = np.concatenate(all_headings)
        else:
            # Fallback to simple trajectory
            n_points = len(time)
            position = np.zeros((n_points, 3))
            velocity = np.zeros((n_points, 3))
            headings = np.zeros(n_points)

        # Resample to match the requested time vector
        n_target = len(time)
        n_generated = len(position)

        if n_generated != n_target:
            # Resample trajectory to match time vector
            indices = np.linspace(0, n_generated - 1, n_target).astype(int)
            position = position[indices]
            velocity = velocity[indices]
            headings = headings[indices]

        # Compute acceleration from velocity changes
        acceleration = np.gradient(velocity, dt, axis=0)

        # Create orientation quaternions from headings
        orientation = np.zeros((len(position), 4))
        for i, heading in enumerate(headings):
            r = Rotation.from_euler('z', heading, degrees=False)
            quat = r.as_quat()  # [x, y, z, w]
            orientation[i] = [quat[3], quat[0], quat[1], quat[2]]  # [w, x, y, z]

        # Angular velocity from orientation changes
        angular_velocity = self._compute_angular_velocity(orientation, dt)

        return GroundTruth(time, position, velocity, acceleration,
                          orientation, angular_velocity)

    def _generate_survey_lines(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate survey lines trajectory with smooth turns (realistic bathymetric mapping pattern)"""
        # Survey parameters from config
        line_length = getattr(config, 'line_length', 500.0)
        line_spacing = config.survey_spacing
        num_lines = config.survey_lines
        vessel_speed = config.vessel_speed  # Use constant vessel speed
        requested_duration = config.duration

        # For survey lines, turn radius should be half the line spacing
        # This creates a perfect semicircular turn that fits exactly between lines
        turn_radius = line_spacing / 2.0

        # Calculate total distance for the survey pattern (for information only)
        total_line_distance = num_lines * line_length
        total_turn_distance = (num_lines - 1) * np.pi * turn_radius  # Semicircular turns
        total_distance = total_line_distance + total_turn_distance

        # Calculate estimated time to complete full survey at constant speed
        estimated_completion_time = total_distance / vessel_speed

        logger.info(f"Survey lines: {num_lines} lines, line_length={line_length}m, "
                   f"line_spacing={line_spacing}m, turn_radius={turn_radius}m")
        logger.info(f"Total distance: {total_distance:.1f}m, vessel_speed: {vessel_speed:.2f}m/s")
        logger.info(f"Estimated completion time: {estimated_completion_time:.1f}s, "
                   f"simulation duration: {requested_duration:.1f}s")

        if estimated_completion_time > requested_duration:
            logger.info(f"Survey pattern will not complete within duration - trajectory will end at {requested_duration}s")

        # Time step
        dt = time[1] - time[0] if len(time) > 1 else 0.01

        # Generate trajectory segments for each survey line
        all_positions = []
        all_velocities = []
        all_headings = []
        current_time = 0.0  # Track elapsed time during trajectory generation

        for line_idx in range(num_lines):
            # Check if we have time remaining
            if current_time >= requested_duration:
                logger.info(f"Duration limit reached at line {line_idx}, stopping trajectory generation")
                break
            north_pos = line_idx * line_spacing

            if line_idx % 2 == 0:
                # Even lines: West to East
                start_east = -line_length / 2
                end_east = line_length / 2
                heading = np.pi/2  # East
            else:
                # Odd lines: East to West
                start_east = line_length / 2
                end_east = -line_length / 2
                heading = -np.pi/2  # West

            # Create straight line segment for this survey line
            start_pos = np.array([north_pos, start_east, -5.0])
            end_pos = np.array([north_pos, end_east, -5.0])

            # Calculate time needed for this segment
            segment_distance = np.linalg.norm(end_pos - start_pos)
            segment_time = segment_distance / vessel_speed

            # Check if we have enough time for the full segment
            remaining_time = requested_duration - current_time
            if segment_time <= remaining_time:
                # Generate full segment
                segment_positions, segment_velocities, segment_headings = self._create_straight_segment(
                    start_pos, end_pos, vessel_speed, dt
                )
                current_time += segment_time
            else:
                # Generate partial segment for remaining time
                partial_distance = vessel_speed * remaining_time
                direction = (end_pos - start_pos) / segment_distance
                partial_end_pos = start_pos + direction * partial_distance

                segment_positions, segment_velocities, segment_headings = self._create_straight_segment(
                    start_pos, partial_end_pos, vessel_speed, dt
                )
                current_time = requested_duration

                all_positions.append(segment_positions)
                all_velocities.append(segment_velocities)
                all_headings.append(segment_headings)
                logger.info(f"Duration limit reached during line {line_idx}, stopping trajectory generation")
                break

            all_positions.append(segment_positions)
            all_velocities.append(segment_velocities)
            all_headings.append(segment_headings)

            # Add turn to next line (except for the last line)
            if line_idx < num_lines - 1 and current_time < requested_duration:
                # Calculate next line parameters
                next_north_pos = (line_idx + 1) * line_spacing
                current_heading = heading

                if (line_idx + 1) % 2 == 0:
                    # Next line is even: West to East
                    next_start_east = -line_length / 2
                    next_heading = np.pi/2  # East
                else:
                    # Next line is odd: East to West
                    next_start_east = line_length / 2
                    next_heading = -np.pi/2  # West

                next_start_pos = np.array([next_north_pos, next_start_east, -5.0])

                # Calculate time needed for the turn
                turn_distance = np.pi * turn_radius  # Semicircular turn
                turn_time = turn_distance / vessel_speed

                # Check if we have enough time for the turn
                remaining_time = requested_duration - current_time
                if turn_time <= remaining_time:
                    # Create smooth U-turn from end of current line to start of next line
                    turn_positions, turn_velocities, turn_headings = self._create_smooth_turn(
                        end_pos, current_heading, next_start_pos, next_heading,
                        turn_radius, vessel_speed, dt
                    )
                    current_time += turn_time

                    all_positions.append(turn_positions)
                    all_velocities.append(turn_velocities)
                    all_headings.append(turn_headings)
                else:
                    logger.info(f"Not enough time for turn after line {line_idx}, stopping trajectory generation")
                    break

        # Concatenate all segments
        if all_positions:
            position = np.vstack(all_positions)
            velocity = np.vstack(all_velocities)
            headings = np.concatenate(all_headings)
        else:
            # Fallback to simple trajectory
            n_points = len(time)
            position = np.zeros((n_points, 3))
            velocity = np.zeros((n_points, 3))
            headings = np.zeros(n_points)

        # Resample to match the requested time vector
        n_target = len(time)
        n_generated = len(position)

        if n_generated != n_target:
            # Resample trajectory to match time vector
            indices = np.linspace(0, n_generated - 1, n_target).astype(int)
            position = position[indices]
            velocity = velocity[indices]
            headings = headings[indices]

        # Compute acceleration from velocity changes
        acceleration = np.gradient(velocity, dt, axis=0)

        # Create orientation quaternions from headings
        orientation = np.zeros((len(position), 4))
        for i, heading in enumerate(headings):
            r = Rotation.from_euler('z', heading, degrees=False)
            quat = r.as_quat()  # [x, y, z, w]
            orientation[i] = [quat[3], quat[0], quat[1], quat[2]]  # [w, x, y, z]

        # Angular velocity from orientation changes
        angular_velocity = self._compute_angular_velocity(orientation, dt)

        return GroundTruth(time, position, velocity, acceleration,
                          orientation, angular_velocity)

    def _generate_straight(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate straight line trajectory"""
        n_points = len(time)

        # Simple straight line along north direction
        distance = config.vessel_speed * time
        position = np.column_stack([distance, np.zeros(n_points), np.zeros(n_points)])

        # Constant velocity
        velocity = np.column_stack([
            np.full(n_points, config.vessel_speed),
            np.zeros(n_points),
            np.zeros(n_points)
        ])

        # Zero acceleration
        acceleration = np.zeros((n_points, 3))

        # Constant orientation (facing north)
        orientation = np.zeros((n_points, 4))
        orientation[:, 0] = 1.0  # w component of quaternion

        # Zero angular velocity
        angular_velocity = np.zeros((n_points, 3))

        return GroundTruth(time, position, velocity, acceleration,
                          orientation, angular_velocity)

    def _generate_spiral(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate spiral trajectory"""
        n_points = len(time)

        # Spiral parameters
        total_turns = config.spiral_turns
        pitch = config.spiral_pitch  # meters per turn

        # Angular velocity (constant)
        total_angle = 2 * np.pi * total_turns
        omega = total_angle / config.duration

        # Radius increases linearly with time
        max_radius = config.radius
        angles = omega * time
        radii = max_radius * time / config.duration

        # Positions
        north = radii * np.cos(angles)
        east = radii * np.sin(angles)
        down = -pitch * time / (config.duration / total_turns)  # Descending spiral

        position = np.column_stack([north, east, down])

        # Numerical derivatives
        dt = time[1] - time[0]
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)

        # Smooth trajectories
        for j in range(3):
            velocity[:, j] = self._smooth_signal(velocity[:, j], window_size=8)
            acceleration[:, j] = self._smooth_signal(acceleration[:, j], window_size=8)

        # Orientation and angular velocity
        orientation = self._compute_orientation_from_velocity(velocity)
        angular_velocity = self._compute_angular_velocity(orientation, dt)

        return GroundTruth(time, position, velocity, acceleration,
                          orientation, angular_velocity)

    def _generate_coastal_patrol(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate coastal patrol trajectory (irregular coastline following)"""
        n_points = len(time)

        # Create irregular coastline using waypoints
        n_waypoints = config.coastal_waypoints
        irregularity = config.coastal_irregularity

        # Generate waypoints along a rough coastline
        waypoint_angles = np.linspace(0, 2 * np.pi, n_waypoints, endpoint=False)
        base_radius = config.radius

        # Add irregularity to create realistic coastline
        waypoint_radii = base_radius * (1 + irregularity * np.random.randn(n_waypoints))
        waypoints = np.column_stack([
            waypoint_radii * np.cos(waypoint_angles),
            waypoint_radii * np.sin(waypoint_angles),
            np.zeros(n_waypoints)
        ])

        # Interpolate between waypoints
        position = np.zeros((n_points, 3))
        for i, t in enumerate(time):
            # Normalized time (0 to 1)
            t_norm = (t % config.duration) / config.duration

            # Find current segment
            segment_progress = t_norm * n_waypoints
            segment_idx = int(segment_progress) % n_waypoints
            next_idx = (segment_idx + 1) % n_waypoints

            # Interpolation factor within segment
            alpha = segment_progress - int(segment_progress)

            # Interpolate position
            position[i] = (1 - alpha) * waypoints[segment_idx] + alpha * waypoints[next_idx]

        # Numerical derivatives
        dt = time[1] - time[0]
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)

        # Smooth trajectories
        for j in range(3):
            velocity[:, j] = self._smooth_signal(velocity[:, j], window_size=8)
            acceleration[:, j] = self._smooth_signal(acceleration[:, j], window_size=8)

        # Orientation and angular velocity
        orientation = self._compute_orientation_from_velocity(velocity)
        angular_velocity = self._compute_angular_velocity(orientation, dt)

        return GroundTruth(time, position, velocity, acceleration,
                          orientation, angular_velocity)

    def _smooth_signal(self, signal: np.ndarray, window_size: int = 5) -> np.ndarray:
        """Apply simple moving average smoothing"""
        if len(signal) < window_size:
            return signal
        
        smoothed = np.copy(signal)
        half_window = window_size // 2
        
        for i in range(half_window, len(signal) - half_window):
            smoothed[i] = np.mean(signal[i-half_window:i+half_window+1])
        
        return smoothed
    
    def _compute_orientation_from_velocity(self, velocity: np.ndarray) -> np.ndarray:
        """Compute orientation quaternions from velocity vectors"""
        n_points = velocity.shape[0]
        orientation = np.zeros((n_points, 4))
        
        for i in range(n_points):
            vel = velocity[i]
            speed = np.linalg.norm(vel)
            
            if speed > 0.1:  # Avoid division by zero
                # Heading from velocity
                heading = np.arctan2(vel[1], vel[0])  # East, North
                
                # Create rotation from heading
                r = Rotation.from_euler('z', heading, degrees=False)
                quat = r.as_quat()  # [x, y, z, w]
                orientation[i] = [quat[3], quat[0], quat[1], quat[2]]  # [w, x, y, z]
            else:
                # No movement, maintain previous orientation
                if i > 0:
                    orientation[i] = orientation[i-1]
                else:
                    orientation[i] = [1, 0, 0, 0]  # Identity quaternion
        
        return orientation
    
    def _compute_angular_velocity(self, orientation: np.ndarray, dt: float) -> np.ndarray:
        """Compute angular velocity from orientation changes"""
        n_points = orientation.shape[0]
        angular_velocity = np.zeros((n_points, 3))
        
        for i in range(1, n_points):
            # Current and previous quaternions
            q_curr = orientation[i]
            q_prev = orientation[i-1]
            
            # Relative rotation
            r_curr = Rotation.from_quat([q_curr[1], q_curr[2], q_curr[3], q_curr[0]])
            r_prev = Rotation.from_quat([q_prev[1], q_prev[2], q_prev[3], q_prev[0]])
            
            # Relative rotation
            r_rel = r_curr * r_prev.inv()
            
            # Convert to angular velocity
            rotvec = r_rel.as_rotvec()
            angular_velocity[i] = rotvec / dt
        
        # Set first point same as second
        angular_velocity[0] = angular_velocity[1] if n_points > 1 else np.zeros(3)
        
        return angular_velocity

    def _create_smooth_turn(self, start_pos: np.ndarray, start_heading: float,
                           end_pos: np.ndarray, end_heading: float,
                           min_turn_radius: float, speed: float, dt: float) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Create a smooth turn between two waypoints using circular arc

        Args:
            start_pos: Starting position [north, east, down]
            start_heading: Starting heading in radians (0 = North)
            end_pos: Ending position [north, east, down]
            end_heading: Ending heading in radians
            min_turn_radius: Minimum turning radius in meters
            speed: Vehicle speed in m/s
            dt: Time step in seconds

        Returns:
            Tuple of (positions, velocities, headings) arrays for the turn
        """
        # Calculate the turn geometry
        heading_change = end_heading - start_heading

        # Normalize heading change to [-pi, pi]
        while heading_change > np.pi:
            heading_change -= 2 * np.pi
        while heading_change < -np.pi:
            heading_change += 2 * np.pi

        # If heading change is small, use straight line
        if abs(heading_change) < 0.1:  # ~6 degrees
            return self._create_straight_segment(start_pos, end_pos, speed, dt)

        # Calculate turn radius (use minimum or larger if needed for geometry)
        turn_radius = max(min_turn_radius, abs(heading_change) * min_turn_radius / np.pi)

        # Calculate turn center
        turn_direction = 1 if heading_change > 0 else -1  # 1 for left turn, -1 for right

        # Center is perpendicular to start heading at turn_radius distance
        center_offset_heading = start_heading + turn_direction * np.pi/2
        turn_center = start_pos[:2] + turn_radius * np.array([
            np.cos(center_offset_heading),
            np.sin(center_offset_heading)
        ])

        # Calculate arc length and time
        arc_length = abs(heading_change) * turn_radius
        turn_time = arc_length / speed
        n_points = max(int(turn_time / dt), 2)

        # Generate turn trajectory
        positions = np.zeros((n_points, 3))
        velocities = np.zeros((n_points, 3))
        headings = np.zeros(n_points)

        for i in range(n_points):
            progress = i / (n_points - 1)
            current_heading = start_heading + progress * heading_change

            # Position on circle
            angle_from_center = current_heading - turn_direction * np.pi/2
            pos_2d = turn_center + turn_radius * np.array([
                np.cos(angle_from_center),
                np.sin(angle_from_center)
            ])

            positions[i] = [pos_2d[0], pos_2d[1], start_pos[2]]  # Keep same depth
            headings[i] = current_heading

            # Velocity tangent to circle
            velocities[i] = speed * np.array([
                np.cos(current_heading),
                np.sin(current_heading),
                0.0
            ])

        return positions, velocities, headings

    def _create_straight_segment(self, start_pos: np.ndarray, end_pos: np.ndarray,
                                speed: float, dt: float) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Create a straight line segment between two points

        Args:
            start_pos: Starting position [north, east, down]
            end_pos: Ending position [north, east, down]
            speed: Vehicle speed in m/s
            dt: Time step in seconds

        Returns:
            Tuple of (positions, velocities, headings) arrays for the segment
        """
        # Calculate direction and distance
        direction_2d = end_pos[:2] - start_pos[:2]
        distance = np.linalg.norm(direction_2d)

        if distance < 1e-6:  # Very short segment
            return np.array([start_pos]), np.array([[0, 0, 0]]), np.array([0])

        # Calculate heading
        heading = np.arctan2(direction_2d[1], direction_2d[0])

        # Calculate time and points
        segment_time = distance / speed
        n_points = max(int(segment_time / dt), 2)

        # Generate straight trajectory
        positions = np.zeros((n_points, 3))
        velocities = np.zeros((n_points, 3))
        headings = np.full(n_points, heading)

        for i in range(n_points):
            progress = i / (n_points - 1)
            positions[i] = start_pos + progress * (end_pos - start_pos)
            velocities[i] = speed * np.array([np.cos(heading), np.sin(heading), 0.0])

        return positions, velocities, headings

class SensorSimulator:
    """Simulates all sensor measurements"""
    
    def __init__(self):
        # Standard gravity (constant)
        self.gravity = np.array([0, 0, STANDARD_GRAVITY])  # m/s², NED frame
        # Magnetic field will be calculated from environment config
    
    def simulate_sensors(self, ground_truth: GroundTruth,
                        sensor_config: SensorConfig,
                        env_config: EnvironmentConfig,
                        traj_config: TrajectoryConfig) -> SensorMeasurements:
        """Simulate all sensor measurements"""
        
        # Generate individual sensor measurements
        accel_data = self._simulate_accelerometer(ground_truth, sensor_config)
        gyro_data = self._simulate_gyroscope(ground_truth, sensor_config)
        mag_data = self._simulate_magnetometer(ground_truth, sensor_config, env_config)
        gps_pos_data = self._simulate_gps_position(ground_truth, sensor_config, env_config, traj_config)
        gps_vel_data = self._simulate_gps_velocity(ground_truth, sensor_config, env_config)
        
        # Create time vectors for each sensor
        time_vectors = {
            'accelerometer': accel_data['time'],
            'gyroscope': gyro_data['time'],
            'magnetometer': mag_data['time'],
            'gps_position': gps_pos_data['time'],
            'gps_velocity': gps_vel_data['time']
        }
        
        return SensorMeasurements(
            time=time_vectors,
            accelerometer=accel_data['measurements'],
            gyroscope=gyro_data['measurements'],
            magnetometer=mag_data['measurements'],
            gps_position=gps_pos_data['measurements'],
            gps_velocity=gps_vel_data['measurements']
        )
    
    def _simulate_accelerometer(self, ground_truth: GroundTruth, 
                               config: SensorConfig) -> Dict:
        """Simulate accelerometer measurements"""
        # Resample to accelerometer rate
        time_accel, indices = self._resample_time_vector(
            ground_truth.time, config.accelerometer_rate
        )
        
        # Get acceleration and orientation at sample points
        acc_ned = ground_truth.acceleration[indices]
        orientation = ground_truth.orientation[indices]
        
        # Transform to body frame and add gravity
        acc_body = np.zeros_like(acc_ned)
        for i, (acc, quat) in enumerate(zip(acc_ned, orientation)):
            # Rotate acceleration to body frame
            r = Rotation.from_quat([quat[1], quat[2], quat[3], quat[0]])
            acc_body[i] = r.apply(acc + self.gravity, inverse=True)
        
        # Add noise
        noise = np.random.normal(0, config.accelerometer_noise, acc_body.shape)
        acc_body += noise
        
        return {'time': time_accel, 'measurements': acc_body}
    
    def _simulate_gyroscope(self, ground_truth: GroundTruth, 
                           config: SensorConfig) -> Dict:
        """Simulate gyroscope measurements"""
        # Resample to gyroscope rate
        time_gyro, indices = self._resample_time_vector(
            ground_truth.time, config.gyroscope_rate
        )
        
        # Get angular velocity at sample points
        omega_body = ground_truth.angular_velocity[indices]
        
        # Add noise
        noise = np.random.normal(0, config.gyroscope_noise, omega_body.shape)
        omega_body += noise
        
        return {'time': time_gyro, 'measurements': omega_body}
    
    def _simulate_magnetometer(self, ground_truth: GroundTruth,
                              config: SensorConfig,
                              env_config: EnvironmentConfig) -> Dict:
        """Simulate magnetometer measurements"""
        # Resample to magnetometer rate
        time_mag, indices = self._resample_time_vector(
            ground_truth.time, config.magnetometer_rate
        )

        # Get orientation at sample points
        orientation = ground_truth.orientation[indices]

        # Calculate magnetic field from environment config
        mag_strength = env_config.magnetic_field_strength
        mag_declination = np.radians(env_config.magnetic_declination)
        mag_inclination = np.radians(env_config.magnetic_inclination)

        # Magnetic field in NED frame
        earth_magnetic_field = np.array([
            mag_strength * np.cos(mag_inclination) * np.cos(mag_declination),
            mag_strength * np.cos(mag_inclination) * np.sin(mag_declination),
            mag_strength * np.sin(mag_inclination)
        ])

        # Transform magnetic field to body frame
        mag_body = np.zeros((len(indices), 3))
        for i, quat in enumerate(orientation):
            r = Rotation.from_quat([quat[1], quat[2], quat[3], quat[0]])
            mag_body[i] = r.apply(earth_magnetic_field, inverse=True)
        
        # Add noise
        noise = np.random.normal(0, config.magnetometer_noise, mag_body.shape)
        mag_body += noise
        
        return {'time': time_mag, 'measurements': mag_body}
    
    def _simulate_gps_position(self, ground_truth: GroundTruth,
                              config: SensorConfig,
                              env_config: EnvironmentConfig,
                              traj_config: TrajectoryConfig) -> Dict:
        """Simulate GPS position measurements"""
        # Resample to GPS rate
        time_gps, indices = self._resample_time_vector(
            ground_truth.time, config.gps_position_rate
        )
        
        # Get positions at sample points
        pos_ned = ground_truth.position[indices]

        # Add GPS noise in NED frame (meters), then convert to LLA
        # Generate noise in meters (NED frame)
        noise_ned = np.zeros((len(indices), 3))
        noise_ned[:, 0] = np.random.normal(0, env_config.gps_horizontal_accuracy, len(indices))  # North
        noise_ned[:, 1] = np.random.normal(0, env_config.gps_horizontal_accuracy, len(indices))  # East
        noise_ned[:, 2] = np.random.normal(0, env_config.gps_vertical_accuracy, len(indices))    # Down

        # Add noise to NED positions, then convert to LLA
        pos_ned_noisy = pos_ned + noise_ned

        # Use standardized coordinate transformation
        from utils.coordinate_transforms import ned_to_lla
        ref_lla = (traj_config.center_lat, traj_config.center_lon, traj_config.center_alt)
        pos_lla = ned_to_lla(pos_ned_noisy, ref_lla)
        
        # Apply GPS outages
        if env_config.gps_outage_probability > 0:
            outage_mask = np.random.random(len(indices)) < env_config.gps_outage_probability
            pos_lla[outage_mask] = np.nan
        
        return {'time': time_gps, 'measurements': pos_lla}
    
    def _simulate_gps_velocity(self, ground_truth: GroundTruth, 
                              config: SensorConfig, 
                              env_config: EnvironmentConfig) -> Dict:
        """Simulate GPS velocity measurements"""
        # Resample to GPS velocity rate
        time_gps, indices = self._resample_time_vector(
            ground_truth.time, config.gps_velocity_rate
        )
        
        # Get velocities at sample points
        vel_ned = ground_truth.velocity[indices]
        
        # Add noise
        noise = np.random.normal(0, env_config.gps_velocity_accuracy, vel_ned.shape)
        vel_ned += noise
        
        # Apply GPS outages
        if env_config.gps_outage_probability > 0:
            outage_mask = np.random.random(len(indices)) < env_config.gps_outage_probability
            vel_ned[outage_mask] = np.nan
        
        return {'time': time_gps, 'measurements': vel_ned}
    
    def _resample_time_vector(self, original_time: np.ndarray, 
                             target_rate: float) -> Tuple[np.ndarray, np.ndarray]:
        """Resample time vector to target rate"""
        dt_target = 1.0 / target_rate
        duration = original_time[-1] - original_time[0]
        
        # New time vector
        new_time = np.arange(original_time[0], original_time[-1], dt_target)
        
        # Find closest indices in original time
        indices = np.searchsorted(original_time, new_time)
        indices = np.clip(indices, 0, len(original_time) - 1)
        
        return new_time, indices
    


class SimulationEngine:
    """Main simulation orchestrator"""
    
    def __init__(self):
        self.trajectory_generator = TrajectoryGenerator()
        self.sensor_simulator = SensorSimulator()
    
    def run_simulation(self, traj_config: TrajectoryConfig, 
                      sensor_config: SensorConfig, 
                      env_config: EnvironmentConfig) -> SimulationData:
        """Run complete simulation"""
        
        logger.info("Starting trajectory generation...")
        ground_truth = self.trajectory_generator.generate_trajectory(traj_config)
        
        logger.info("Starting sensor simulation...")
        sensor_measurements = self.sensor_simulator.simulate_sensors(
            ground_truth, sensor_config, env_config, traj_config
        )
        
        # Package configuration
        config_dict = {
            'trajectory': traj_config,
            'sensors': sensor_config,
            'environment': env_config
        }
        
        logger.info("Simulation completed successfully")
        return SimulationData(ground_truth, sensor_measurements, config_dict)
