#!/usr/bin/env python3
"""
16-State GPS-INS Fusion Filter - Optimized Extended Kalman Filter



Features:
- 16-state continuous-discrete Extended Kalman Filter
- Quaternion kinematics with proper normalization
- Derived acceleration and angular velocity from sensor measurements
- Complete mathematical derivations for all Jacobians


State Vector (16 elements):
[0:3]   - Position NED (m) - navigation frame
[3:6]   - Velocity NED (m/s) - navigation frame  
[6:10]  - Quaternion (w, x, y, z) - orientation from navigation to body frame
[10:13] - Accelerometer bias (m/s²) - body frame
[13:16] - Gyroscope bias (rad/s) - body frame
"""

import numpy as np
from scipy.spatial.transform import Rotation
from scipy.linalg import expm
from typing import Optional, Tuple, Dict, Union
from dataclasses import dataclass
import warnings
import logging
from utils.constants import EARTH_RADIUS, EARTH_FLATTENING, EARTH_ROTATION_RATE, STANDARD_GRAVITY

logger = logging.getLogger(__name__)

@dataclass
class INSFilterConfig:
    """Configuration for the 16-state INS filter"""
    
    # Process noise standard deviations
    position_noise: float = 0.1
    velocity_noise: float = 0.1
    quaternion_noise: float = 1e-3
    accelerometer_bias_noise: float = 1e-7
    gyroscope_bias_noise: float = 1e-7
    
    # Measurement noise (used for sensor fusion)
    acceleration_noise: float = 0.1
    angular_velocity_noise: float = 0.001
    
    # Initial state covariance diagonal values
    position_covariance: float = 10.0
    velocity_covariance: float = 1.0
    quaternion_covariance: float = 1e-2
    accelerometer_bias_covariance: float = 1e-3
    gyroscope_bias_covariance: float = 1e-3
    
    # Earth model parameters
    earth_rotation_rate: float = EARTH_ROTATION_RATE
    earth_equatorial_radius: float = EARTH_RADIUS
    earth_flattening: float = EARTH_FLATTENING
    gravity_magnitude: float = STANDARD_GRAVITY
    
    # Numerical parameters
    max_dt: float = 0.1
    quaternion_norm_threshold: float = 1e-6

class ProductionINSFilter:
    """
    Production-grade 16-state GPS/INS Extended Kalman Filter
    
    """
    
    def __init__(self, 
                 reference_location: Tuple[float, float, float],
                 config: Optional[INSFilterConfig] = None):
        """
        Initialize the 16-state INS filter
        
        Args:
            reference_location: (latitude, longitude, altitude) in degrees and meters
            config: Filter configuration parameters
        """
        self.config = config or INSFilterConfig()
        
        # Reference location for NED frame
        self.ref_latitude = np.radians(reference_location[0])
        self.ref_longitude = np.radians(reference_location[1])
        self.ref_altitude = reference_location[2]
        
        # Precompute reference location parameters
        self._compute_reference_parameters()
        
        # Initialize 16-element state vector
        self.state = np.zeros(16)
        self.state[6] = 1.0  # Initialize with identity quaternion [w,x,y,z]
        
        # Initialize state covariance matrix (16x16)
        self.P = self._initialize_covariance_matrix()
        
        # Prediction timestamp for dt calculation
        self.last_prediction_time = None
        
        # State indices for clarity
        self.POS_IDX = slice(0, 3)
        self.VEL_IDX = slice(3, 6)
        self.QUAT_IDX = slice(6, 10)
        self.ACC_BIAS_IDX = slice(10, 13)
        self.GYRO_BIAS_IDX = slice(13, 16)
        
        # Storage for derived quantities (for API compatibility)
        self.derived_acceleration = np.zeros(3)
        self.derived_angular_velocity = np.zeros(3)
        
        # Fixed geomagnetic field (NED frame) - typical values for Earth
        self.geomagnetic_field_ned = np.array([21000.0, 0.0, 42000.0])  # nT
        
        self.latest_gyro_measurement = None
        
        logger.info(f"16-state INS filter initialized at reference: {reference_location}")
    
    def _compute_reference_parameters(self):
        """Compute reference location dependent parameters"""
        lat = self.ref_latitude
        
        # Earth model parameters
        a = self.config.earth_equatorial_radius
        f = self.config.earth_flattening
        e2 = 2*f - f**2  # First eccentricity squared
        
        # Radius of curvature in meridian
        self.M = a * (1 - e2) / (1 - e2 * np.sin(lat)**2)**(3/2)
        
        # Radius of curvature in prime vertical
        self.N = a / np.sqrt(1 - e2 * np.sin(lat)**2)
        
        # Local gravity (simplified model)
        self.local_gravity = self.config.gravity_magnitude * (
            1 + 5.3024e-3 * np.sin(lat)**2 - 5.8e-6 * np.sin(2*lat)**2
        )
        
        # Gravity vector in NED frame
        self.gravity_ned = np.array([0.0, 0.0, self.local_gravity])
        
        logger.info(f"Local gravity: {self.local_gravity:.3f} m/s²")
    
    def _initialize_covariance_matrix(self) -> np.ndarray:
        """Initialize the 16x16 state covariance matrix"""
        P_diag = np.array([
            # Position (3)
            self.config.position_covariance,
            self.config.position_covariance,
            self.config.position_covariance,
            # Velocity (3)
            self.config.velocity_covariance,
            self.config.velocity_covariance,
            self.config.velocity_covariance,
            # Quaternion (4)
            self.config.quaternion_covariance,
            self.config.quaternion_covariance,
            self.config.quaternion_covariance,
            self.config.quaternion_covariance,
            # Accelerometer bias (3)
            self.config.accelerometer_bias_covariance,
            self.config.accelerometer_bias_covariance,
            self.config.accelerometer_bias_covariance,
            # Gyroscope bias (3)
            self.config.gyroscope_bias_covariance,
            self.config.gyroscope_bias_covariance,
            self.config.gyroscope_bias_covariance
        ])
        
        return np.diag(P_diag)
        
    
    def predict(self, dt: float, timestamp: Optional[float] = None):
        """
        Predict state forward using continuous-discrete EKF
        
        Args:
            dt: Time step in seconds
            timestamp: Optional timestamp for automatic dt calculation
        """
        if timestamp is not None and self.last_prediction_time is not None:
            dt = timestamp - self.last_prediction_time
        
        # Clamp dt to maximum value for numerical stability
        dt = min(dt, self.config.max_dt)
        
        if dt <= 0:
            warnings.warn("Non-positive time step in prediction")
            return
        
        # Extract current state components
        pos = self.state[self.POS_IDX].copy()
        vel = self.state[self.VEL_IDX].copy()
        q = self.state[self.QUAT_IDX].copy()
        gyro_bias = self.state[self.GYRO_BIAS_IDX].copy()
        
        # Normalize quaternion
        q = self._normalize_quaternion(q)
        self.state[self.QUAT_IDX] = q
        
        # State propagation
        # Position: p_k+1 = p_k + v_k * dt (simple integration)
        self.state[self.POS_IDX] = pos + vel * dt
        
        # Velocity: v_k+1 = v_k (constant velocity model - NO GRAVITY HERE!)
        # self.state[self.VEL_IDX] = vel  # No change needed
        
        # Quaternion: propagate using gyroscope if available
        if hasattr(self, 'latest_gyro_measurement') and self.latest_gyro_measurement is not None:
            # Use corrected angular velocity
            omega = self.latest_gyro_measurement - gyro_bias
            # Propagate quaternion
            self.state[self.QUAT_IDX] = self._propagate_quaternion_from_gyro(q, omega, dt)
        # else: quaternion remains constant if no gyroscope data
        
        # Biases remain constant (random walk model)
        
        # Compute state transition matrix F (16x16)
        F = self._compute_state_transition_matrix(dt)
        
        # Compute process noise covariance Q (16x16)
        Q = self._compute_process_noise_matrix(dt)
        
        # Propagate covariance: P = F*P*F' + Q
        self.P = F @ self.P @ F.T + Q
        
        # Ensure covariance symmetry
        self.P = 0.5 * (self.P + self.P.T)
        
        # Update timestamp
        if timestamp is not None:
            self.last_prediction_time = timestamp
    
    def _compute_quaternion_process_noise(self, dt: float) -> np.ndarray:
        """Compute quaternion process noise in the tangent space"""
        # Angular velocity process noise (3x3)
        sigma_omega = self.config.quaternion_noise
        Q_omega = np.eye(3) * sigma_omega**2
        
        # Map to quaternion space using the error-state Jacobian
        # For small dt: ΔQ ≈ [1, 0.5*ω*dt]
        # This gives a 4x3 mapping from angular velocity to quaternion
        G = 0.5 * dt * np.array([
            [0, 0, 0],      # w component
            [1, 0, 0],      # x component  
            [0, 1, 0],      # y component
            [0, 0, 1]       # z component
        ])
        
        # Quaternion process noise: Q_q = G * Q_omega * G^T
        Q_quaternion = G @ Q_omega @ G.T
        
        return Q_quaternion
    
    def _normalize_quaternion(self, q: np.ndarray) -> np.ndarray:
        """Normalize quaternion with numerical stability check"""
        norm = np.linalg.norm(q)
        if norm < self.config.quaternion_norm_threshold:
            warnings.warn("Quaternion norm too small, resetting to identity")
            return np.array([1.0, 0.0, 0.0, 0.0])
        return q / norm
    
    def _compute_state_transition_matrix(self, dt: float) -> np.ndarray:
        """Compute the 16x16 state transition matrix F"""
        F = np.eye(16)
        
        # Position derivatives
        F[0:3, 3:6] = np.eye(3) * dt  # ∂pos/∂vel
        
        # Velocity derivatives (gravity effect already included in propagation)
        # F[3:6, 3:6] remains identity
        
        # Quaternion derivatives remain identity (small angle assumption)
        # F[6:10, 6:10] remains identity
        
        # Bias derivatives remain identity (random walk)
        # F[10:13, 10:13] and F[13:16, 13:16] remain identity
        
        return F
    
    def _compute_process_noise_matrix(self, dt: float) -> np.ndarray:
        """Compute 16x16 process noise covariance matrix Q"""
        Q = np.zeros((16, 16))
        
        # Position process noise (integrated from velocity uncertainty)
        vel_var = self.config.velocity_noise**2
        Q[0:3, 0:3] = np.eye(3) * vel_var * dt**3 / 3
        Q[0:3, 3:6] = np.eye(3) * vel_var * dt**2 / 2
        Q[3:6, 0:3] = np.eye(3) * vel_var * dt**2 / 2
        Q[3:6, 3:6] = np.eye(3) * vel_var * dt
        
        # Quaternion process noise
        #### Q[6:10, 6:10] = np.eye(4) * self.config.quaternion_noise**2 * dt
        # In _compute_process_noise_matrix:
        Q[6:10, 6:10] = self._compute_quaternion_process_noise(dt)
        
        
        # Bias process noises (random walk)
        Q[10:13, 10:13] = np.eye(3) * self.config.accelerometer_bias_noise**2 * dt
        Q[13:16, 13:16] = np.eye(3) * self.config.gyroscope_bias_noise**2 * dt
        
        return Q
    
    def fuse_accelerometer(self, 
                          measurement: np.ndarray, 
                          noise_variance: Union[float, np.ndarray]):
        """
        Fuse 3-axis accelerometer measurement
        
        Args:
            measurement: Accelerometer reading [ax, ay, az] in m/s² (body frame)
            noise_variance: Measurement noise variance
        """
        # Extract state components
        q = self.state[self.QUAT_IDX]
        acc_bias = self.state[self.ACC_BIAS_IDX]
        
        # Measurement model: accel_measured = R_nb * (gravity_ned) + bias
        # where R_nb rotates from NED to body frame
        gravity_body = self._rotate_ned_to_body(self.gravity_ned, q)
        h = gravity_body + acc_bias
        
        # Innovation
        innovation = measurement - h
        
        # Measurement Jacobian H (3x16)
        H = np.zeros((3, 16))
        
        # ∂h/∂quaternion (gravity rotation)
        H[0:3, 6:10] = self._vector_rotation_quaternion_jacobian(q, self.gravity_ned)
        
        # ∂h/∂accelerometer_bias
        H[0:3, 10:13] = np.eye(3)
        
        # Measurement noise covariance
        if np.isscalar(noise_variance):
            R = np.eye(3) * noise_variance
        else:
            R = np.atleast_2d(noise_variance)
        
        # Kalman update
        self._kalman_update(innovation, H, R)
        
        # Update derived acceleration for API compatibility
        # accel_ned = R_bn * (measurement - bias) - gravity_ned
        corrected_accel_body = measurement - self.state[self.ACC_BIAS_IDX]
        self.derived_acceleration = self._rotate_body_to_ned(corrected_accel_body, q) - self.gravity_ned
    
    def fuse_gyroscope(self, 
                      measurement: np.ndarray, 
                      noise_variance: Union[float, np.ndarray]):
        """
        Fuse 3-axis gyroscope measurement
        
        For the 16-state filter, gyroscope measurements don't directly update
        the state (no angular velocity state), but we use them for quaternion
        updates via integration and store derived angular velocity.
        
        Args:
            measurement: Gyroscope reading [wx, wy, wz] in rad/s (body frame)
            noise_variance: Measurement noise variance
        """
        # Extract current state
        q = self.state[self.QUAT_IDX]
        gyro_bias = self.state[self.GYRO_BIAS_IDX]
        
        # Corrected angular velocity
        omega_corrected = measurement - gyro_bias
        
        # Store for API compatibility
        self.derived_angular_velocity = omega_corrected.copy()
        
        # For quaternion update, we can use the angular velocity to predict
        # the quaternion change and compare with current quaternion
        # This is an indirect measurement update
        
        # Small time step for quaternion integration (assume 10ms typical)
        dt_gyro = 0.01
        
        # Predicted quaternion from gyroscope
        q_predicted = self._propagate_quaternion_from_gyro(q, omega_corrected, dt_gyro)
        
        # Use the quaternion difference as innovation
        # This is an approximation but works well in practice
        innovation = self._quaternion_error(q_predicted, q)
        
        # Measurement Jacobian H (3x16) - relating angular velocity to quaternion error
        H = np.zeros((3, 16))
        H[0:3, 6:9] = np.eye(3) * dt_gyro  # Small angle approximation
        H[0:3, 13:16] = -np.eye(3) * dt_gyro  # Effect of gyro bias
        
        # Measurement noise covariance
        if np.isscalar(noise_variance):
            R = np.eye(3) * noise_variance * dt_gyro**2
        else:
            R = np.atleast_2d(noise_variance) * dt_gyro**2
        
        # Kalman update
        self._kalman_update(innovation, H, R)
    
    def fuse_magnetometer(self, 
                         measurement: np.ndarray, 
                         noise_variance: Union[float, np.ndarray]):
        """
        Fuse 3-axis magnetometer measurement
        
        Args:
            measurement: Magnetometer reading [mx, my, mz] in µT (body frame)
            noise_variance: Measurement noise variance
        """
        # Extract state components
        q = self.state[self.QUAT_IDX]
        
        # Expected measurement: magnetic field rotated to body frame
        # (assuming zero magnetometer bias for 16-state filter)
        mag_body_expected = self._rotate_ned_to_body(self.geomagnetic_field_ned, q)
        
        # Innovation
        innovation = measurement - mag_body_expected
        
        # Measurement Jacobian H (3x16)
        H = np.zeros((3, 16))
        
        # ∂h/∂quaternion (magnetic field rotation)
        H[0:3, 6:10] = self._vector_rotation_quaternion_jacobian(q, self.geomagnetic_field_ned)
        
        # Measurement noise covariance
        if np.isscalar(noise_variance):
            R = np.eye(3) * noise_variance
        else:
            R = np.atleast_2d(noise_variance)
        
        # Kalman update
        self._kalman_update(innovation, H, R)
    
    def fuse_gps_position(self, 
                         lla_measurement: np.ndarray, 
                         noise_variance: Union[float, np.ndarray]):
        """
        Fuse GPS position measurement
        
        Args:
            lla_measurement: GPS position [lat, lon, alt] in degrees and meters
            noise_variance: Measurement noise variance (3D: [north_var, east_var, down_var])
        """
        # Convert LLA to NED
        pos_ned_measured = self._lla_to_ned(lla_measurement)
        
        # Extract state position
        pos_ned_state = self.state[self.POS_IDX]
        
        # Innovation
        innovation = pos_ned_measured - pos_ned_state
        
        # Measurement Jacobian H (3x16)
        H = np.zeros((3, 16))
        H[0:3, 0:3] = np.eye(3)  # ∂h/∂position
        
        # Measurement noise covariance
        if isinstance(noise_variance, (list, tuple)) and len(noise_variance) == 3:
            # 3D noise: [north_var, east_var, down_var]
            R = np.diag(noise_variance)
        else:
            raise ValueError(f"GPS position noise must be 3D [north_var, east_var, down_var], got: {noise_variance}")
        
        # Kalman update
        self._kalman_update(innovation, H, R)
    
    def fuse_gps_velocity(self, 
                         velocity_measurement: np.ndarray, 
                         noise_variance: Union[float, np.ndarray]):
        """
        Fuse GPS velocity measurement
        
        Args:
            velocity_measurement: GPS velocity [vn, ve, vd] in m/s (NED frame)
            noise_variance: Measurement noise variance
        """
        # Extract state velocity
        vel_ned_state = self.state[self.VEL_IDX]
        
        # Innovation
        innovation = velocity_measurement - vel_ned_state
        
        # Measurement Jacobian H (3x16)
        H = np.zeros((3, 16))
        H[0:3, 3:6] = np.eye(3)  # ∂h/∂velocity
        
        # Measurement noise covariance
        if np.isscalar(noise_variance):
            R = np.eye(3) * noise_variance
        else:
            R = np.atleast_2d(noise_variance)
        
        # Kalman update
        self._kalman_update(innovation, H, R)
    
    def _kalman_update(self, innovation: np.ndarray, H: np.ndarray, R: np.ndarray):
        """
        Perform Kalman filter update step
        
        Args:
            innovation: Measurement innovation vector
            H: Measurement Jacobian matrix
            R: Measurement noise covariance matrix
        """
        # Innovation covariance
        S = H @ self.P @ H.T + R
        
        # Kalman gain
        try:
            K = self.P @ H.T @ np.linalg.inv(S)
        except np.linalg.LinAlgError:
            warnings.warn("Singular innovation covariance matrix")
            return
        
        # State update
        self.state += K @ innovation
        
        # Normalize quaternion after update
        self.state[self.QUAT_IDX] = self._normalize_quaternion(self.state[self.QUAT_IDX])
        
        # Covariance update (Joseph form for numerical stability)
        I_KH = np.eye(16) - K @ H
        self.P = I_KH @ self.P @ I_KH.T + K @ R @ K.T
        
        # Ensure symmetry
        self.P = 0.5 * (self.P + self.P.T)
    
    def _propagate_quaternion_from_gyro(self, q: np.ndarray, omega: np.ndarray, dt: float) -> np.ndarray:
        """Propagate quaternion using angular velocity"""
        omega_norm = np.linalg.norm(omega)
        
        if omega_norm < 1e-8:
            return self._normalize_quaternion(q)
        
        # Quaternion kinematics matrix
        Omega = np.array([
            [0,        -omega[0], -omega[1], -omega[2]],
            [omega[0],  0,         omega[2], -omega[1]],
            [omega[1], -omega[2],  0,         omega[0]],
            [omega[2],  omega[1], -omega[0],  0       ]
        ])
        
        # Exact solution using matrix exponential
        q_dot_matrix = 0.5 * Omega
        q_new = expm(q_dot_matrix * dt) @ q
        
        return self._normalize_quaternion(q_new)
    
    def _quaternion_error(self, q1: np.ndarray, q2: np.ndarray) -> np.ndarray:
        """Compute quaternion error as rotation vector"""
        # Quaternion difference: q_error = q1 * q2^(-1)
        q2_inv = np.array([q2[0], -q2[1], -q2[2], -q2[3]])  # Conjugate for unit quaternion
        q_error = self._quaternion_multiply(q1, q2_inv)
        
        # Convert to rotation vector (small angle approximation)
        if q_error[0] < 0:  # Ensure scalar part is positive
            q_error = -q_error
        
        # Small angle: [x, y, z] ≈ 2 * [qx, qy, qz] when qw ≈ 1
        return 2.0 * q_error[1:4]
    
    def _quaternion_multiply(self, q1: np.ndarray, q2: np.ndarray) -> np.ndarray:
        """Multiply two quaternions: q1 * q2"""
        w1, x1, y1, z1 = q1
        w2, x2, y2, z2 = q2
        
        return np.array([
            w1*w2 - x1*x2 - y1*y2 - z1*z2,
            w1*x2 + x1*w2 + y1*z2 - z1*y2,
            w1*y2 - x1*z2 + y1*w2 + z1*x2,
            w1*z2 + x1*y2 - y1*x2 + z1*w2
        ])
    
    def _vector_rotation_quaternion_jacobian(self, q: np.ndarray, vector_ned: np.ndarray) -> np.ndarray:
        """
        Compute Jacobian of vector rotation (NED to body) w.r.t. quaternion
        
        For vector v_body = R(q) * v_ned, compute ∂v_body/∂q
        """
        w, x, y, z = q
        vx, vy, vz = vector_ned
        
        # Analytical derivative of rotation matrix elements w.r.t. quaternion
        jacobian = 2 * np.array([
            # ∂v_body_x/∂q
            [w*vx + z*vy - y*vz, x*vx + y*vy + z*vz, -y*vx + x*vy + w*vz, -z*vx - w*vy + x*vz],
            # ∂v_body_y/∂q  
            [-z*vx + w*vy + x*vz, y*vx - x*vy - w*vz, x*vx + y*vy + z*vz, w*vx + z*vy - y*vz],
            # ∂v_body_z/∂q
            [y*vx - x*vz + w*vz, z*vx + w*vy - x*vz, -w*vx - z*vy + y*vz, x*vx + y*vy + z*vz]
        ])
        
        return jacobian
    
    def _rotate_ned_to_body(self, vector_ned: np.ndarray, q: np.ndarray) -> np.ndarray:
        """Rotate vector from NED frame to body frame using quaternion"""
        rotation = Rotation.from_quat([q[1], q[2], q[3], q[0]])  # scipy uses [x,y,z,w]
        return rotation.apply(vector_ned, inverse=True)
    
    def _rotate_body_to_ned(self, vector_body: np.ndarray, q: np.ndarray) -> np.ndarray:
        """Rotate vector from body frame to NED frame using quaternion"""
        rotation = Rotation.from_quat([q[1], q[2], q[3], q[0]])  # scipy uses [x,y,z,w]
        return rotation.apply(vector_body)
    
    def _lla_to_ned(self, lla: np.ndarray) -> np.ndarray:
        """
        Convert geodetic coordinates to NED position
        
        Args:
            lla: [latitude, longitude, altitude] in degrees and meters
            
        Returns:
            NED position [north, east, down] in meters
        """
        # Use standardized coordinate transformation for consistency
        from utils.coordinate_transforms import lla_to_ned
        
        ref_lla = (np.degrees(self.ref_latitude), np.degrees(self.ref_longitude), self.ref_altitude)
        lla_array = lla.reshape(1, -1) if lla.ndim == 1 else lla
        ned_result = lla_to_ned(lla_array, ref_lla)
        
        return ned_result[0] if lla.ndim == 1 else ned_result
    
    # API Compatibility Methods
    def get_state(self) -> np.ndarray:
        """Get complete 16-element state vector"""
        return self.state.copy()
    
    def get_position_ned(self) -> np.ndarray:
        """Get position in NED frame [north, east, down] in meters"""
        return self.state[self.POS_IDX].copy()
    
    def get_velocity_ned(self) -> np.ndarray:
        """Get velocity in NED frame [vn, ve, vd] in m/s"""
        return self.state[self.VEL_IDX].copy()
    
    def get_acceleration_ned(self) -> np.ndarray:
        """Get derived acceleration in NED frame [an, ae, ad] in m/s²"""
        return self.derived_acceleration.copy()
    
    def get_orientation_quaternion(self) -> np.ndarray:
        """Get orientation quaternion [w, x, y, z] (navigation to body)"""
        return self._normalize_quaternion(self.state[self.QUAT_IDX].copy())
    
    def get_angular_velocity(self) -> np.ndarray:
        """Get derived angular velocity [wx, wy, wz] in rad/s (body frame)"""
        return self.derived_angular_velocity.copy()
    
    def get_sensor_biases(self) -> Dict[str, np.ndarray]:
        """Get all sensor biases"""
        return {
            'accelerometer': self.state[self.ACC_BIAS_IDX].copy(),
            'gyroscope': self.state[self.GYRO_BIAS_IDX].copy(),
            'magnetometer': np.zeros(3)  # Not estimated in 16-state filter
        }
    
    def get_geomagnetic_field(self) -> np.ndarray:
        """Get geomagnetic field in NED frame [mx, my, mz] in µT"""
        return self.geomagnetic_field_ned.copy() / 1000.0  # Convert nT to µT
    
    def get_covariance_matrix(self) -> np.ndarray:
        """Get 16x16 state covariance matrix"""
        return self.P.copy()
    
    def set_state(self, state: np.ndarray):
        """Set complete 16-element state vector"""
        if len(state) != 16:
            raise ValueError("State vector must have 16 elements")
        self.state = state.copy()
        self.state[self.QUAT_IDX] = self._normalize_quaternion(self.state[self.QUAT_IDX])
    
    def set_geomagnetic_field(self, magnetic_field_ned: np.ndarray):
        """Set geomagnetic field vector in NED frame [mx, my, mz] in µT"""
        self.geomagnetic_field_ned = magnetic_field_ned.copy() * 1000.0  # Convert µT to nT for storage
    
    def initialize_from_first_gps(self, first_gps_lla: np.ndarray):
        """
        Initialize position state from first GPS reading for faster convergence
        
        Args:
            first_gps_lla: First GPS reading [lat, lon, alt] in degrees and meters
        """
        initial_pos_ned = self._lla_to_ned(first_gps_lla)
        self.state[self.POS_IDX] = initial_pos_ned
        
        # Reduce position uncertainty since we have a GPS fix
        self.P[0:3, 0:3] = np.eye(3) * 1.0  # 1m standard deviation
        
        logger.info(f"16-state filter initialized with GPS position: {first_gps_lla}")
        logger.info(f"Corresponding NED position: {initial_pos_ned}")
    
    def reset_biases(self):
        """Reset all sensor biases to zero"""
        self.state[self.ACC_BIAS_IDX] = 0.0
        self.state[self.GYRO_BIAS_IDX] = 0.0
        