# Altitude Error vs Relative Time Plot - Issue Analysis and Fix

## Problem Identified

The "Altitude Error vs Relative Time" plot in the Pohang Report tab was implemented incorrectly, leading to confusing and potentially misleading results. The issue was in the coordinate system transformation and error calculation approach.

## Root Cause Analysis

### 1. **Inconsistent Error Calculation Methods**

The system used **two different approaches** for calculating positioning errors:

#### **2D Positioning Error** (Correct approach):
```python
# Direct coordinate differences
error_north = est_north - base_north
error_east = est_east - base_east
error_2d = sqrt(error_north² + error_east²)
```

#### **Altitude Error** (Problematic approach):
```python
# Convert down coordinates to altitude first, then calculate error
est_altitude = -est_down
base_altitude = -base_down
altitude_error = est_altitude - base_altitude
# This equals: -(est_down - base_down)
```

### 2. **Mathematical Issue**

The altitude error calculation was **negating the down error**:
- `altitude_error = (-est_down) - (-base_down) = -(est_down - base_down)`
- This created a **sign inversion** compared to the direct error calculation
- The result was the **negative** of the actual vertical positioning error

### 3. **Interpretation Problems**

This sign inversion caused confusion:
- If estimated trajectory was **above** baseline → showed as **negative altitude error**
- If estimated trajectory was **below** baseline → showed as **positive altitude error**
- The error magnitude was correct, but the **sign convention was inverted**

## Coordinate System Context

### **NED Coordinate System** (Used in the system):
- **North**: Positive northward
- **East**: Positive eastward  
- **Down**: Positive downward (below reference)

### **Altitude vs Down Relationship**:
- `altitude = -down` (altitude increases as down decreases)
- `down = -altitude` (down increases as altitude decreases)

### **Error Calculation Logic**:
- **Positive down error**: Estimated trajectory is **below** baseline
- **Negative down error**: Estimated trajectory is **above** baseline
- **Positive altitude error** (old method): Actually means estimated trajectory is **below** baseline (confusing!)

## Solution Implemented

### 1. **Consistent Error Calculation**

Changed to use the **same approach** as 2D positioning error:

```python
# New approach: Direct down error calculation
down_error = est_down - base_down

# This is consistent with:
error_north = est_north - base_north
error_east = est_east - base_east
```

### 2. **Improved Data Source Priority**

The fix uses a **hierarchical approach** for data sources:

1. **Primary**: Use `position_error_down` from trajectory comparison (most accurate)
2. **Fallback**: Calculate directly from trajectory data if error data unavailable
3. **Graceful degradation**: Show appropriate message if no data available

### 3. **Clear Interpretation**

Updated labels and documentation:
- **Title**: "Vertical Position Error vs Relative Time" (instead of "Altitude Error")
- **Y-axis**: "Vertical Position Error (m)"
- **Interpretation**: 
  - Positive values = estimated trajectory **below** baseline
  - Negative values = estimated trajectory **above** baseline

### 4. **Enhanced Debugging**

Added data source indication in the statistics box to help with troubleshooting.

## Code Changes Made

### File: `ins_gps_testing_platform/gui/pohang_results_tab.py`

1. **Updated error calculation logic** (lines 484-533)
2. **Changed plot titles and labels** to reflect "Vertical Position Error"
3. **Added hierarchical data source selection**
4. **Improved error handling and user feedback**

## Verification

### Test Script: `test_altitude_error_fix.py`

Created a comprehensive test that demonstrates:
1. **Mathematical relationship**: `old_method = -(new_method)`
2. **Consistency**: New method aligns with 2D positioning error approach
3. **Visual comparison**: Side-by-side plots showing the difference
4. **Statistical validation**: RMS and mean error comparisons

### Expected Results After Fix:

1. **Consistent sign convention** across all error plots
2. **Intuitive interpretation**: Positive = below baseline, Negative = above baseline
3. **Same magnitude** but correct sign compared to old method
4. **Better alignment** with trajectory comparison error data

## Impact Assessment

### **Before Fix**:
- ❌ Confusing sign convention
- ❌ Inconsistent with other error calculations
- ❌ Difficult to interpret results
- ❌ Potential for misunderstanding system performance

### **After Fix**:
- ✅ Consistent error calculation approach
- ✅ Clear and intuitive interpretation
- ✅ Aligned with trajectory comparison methodology
- ✅ Better debugging and data source transparency

## Recommendations

1. **Verify the fix** by running Pohang processing and checking the vertical position error plot
2. **Compare results** with the trajectory comparison error plots to ensure consistency
3. **Update documentation** to reflect the new interpretation
4. **Consider similar issues** in other parts of the codebase where coordinate transformations are used

## Files Modified

- `ins_gps_testing_platform/gui/pohang_results_tab.py` - Main fix implementation
- `ins_gps_testing_platform/test_altitude_error_fix.py` - Verification test (new)
- `ins_gps_testing_platform/ALTITUDE_ERROR_FIX_REPORT.md` - This documentation (new)

## Testing

Run the verification test:
```bash
cd ins_gps_testing_platform
python test_altitude_error_fix.py
```

This will generate a comparison plot showing the difference between old and new methods.
