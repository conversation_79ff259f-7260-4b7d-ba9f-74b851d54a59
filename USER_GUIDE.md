# INS/GPS Testing Platform - User Guide

## Getting Started

### 1. Launch Application
```bash
python main.py
```

### 2. Configuration Tab

#### Trajectory Setup
1. Select trajectory type from dropdown
2. Adjust duration and sampling rate
3. Set trajectory-specific parameters
4. Configure center location (lat/lon)

#### Sensor Configuration
1. Choose IMU quality template
2. Set individual sensor rates
3. Enable/disable sensors for fusion
4. Load sensor templates as needed

#### Environment Settings
1. Select environment condition
2. Adjust GPS accuracy sliders
3. Set environmental parameters

### 3. Run Simulation
1. Click "Run Simulation" button
2. Monitor progress bar
3. Wait for completion message
4. Switch to Visualization tab

### 4. Results Analysis

#### Trajectory Plots
- 2D trajectory comparison
- Position vs time plots
- Speed analysis
- Altitude tracking

#### Error Analysis
- Position error over time
- Error distribution histograms
- Velocity and orientation errors
- Statistical summaries

#### 3D Visualization
- Interactive 3D trajectory
- Vessel orientation display
- GPS measurement points
- Start/end markers

### 5. Data Export
1. Use "Export Data" from File menu
2. Choose format (CSV, MAT, HDF5)
3. Select export location
4. Data saved for external analysis (exported to ../exports/ directory)

## Configuration Tips

### For Accurate Results
- Use Survey-grade sensors
- Set environment to Good/Ideal
- Enable all sensors
- Use appropriate sampling rates

### For Challenging Scenarios
- Use Consumer-grade sensors
- Set environment to Poor/Extreme
- Disable some sensors
- Add GPS outages

### For Performance Testing
- Use long trajectories (>600s)
- Complex patterns (Figure-8, Spiral)
- High sampling rates (200Hz+)
- Multiple sensor types

## Troubleshooting

### Simulation Fails
- Check trajectory parameters
- Reduce duration if memory issues
- Verify sensor rate values
- Ensure positive numeric values

### Poor Visualization
- Clear results between runs
- Check for NaN values in data
- Verify coordinate transformations
- Use reasonable plot ranges

### Export Issues
- Check file permissions
- Verify export directory exists
- Ensure sufficient disk space
- Try different export formats
