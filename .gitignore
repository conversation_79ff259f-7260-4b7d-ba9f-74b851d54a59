# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Image files
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff
*.svg

# Log files
*.log
logs/

# Binary files
*.bin
*.dat

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Project specific exports
exports/
ins_gps_testing_platform/exports/
pohang_results/

# Temporary files
*.tmp
*.temp
.cache/

# Test outputs
test_outputs/
ins_gps_testing_platform/test_outputs/

# Jupyter Notebook
.ipynb_checkpoints

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
/pohang_dataset_001
/pohang_dataset_005
/exports
/exports
/pohang_dataset_001
/pohang_dataset_005/navigation
ins_gps_testing_platform/test_altitude_error_fix.py
ins_gps_testing_platform/test_local_coordinates.py
ins_gps_testing_platform/debug_vertical_coordinates.py
pohang_dataset_005/navigation/gps.txt
pohang_dataset_001/navigation/gps_corrected.txt
exports/pohang_results/coordinate_frames.csv
exports/pohang_results/gps_data.csv
exports/pohang_results/trajectory_data.csv
exports/pohang_results/gps_data.csv
exports/pohang_results/trajectory_data.csv
pohang_dataset_001/navigation/gps_corrected.txt
pohang_dataset_005/navigation/gps.txt
exports/pohang_results/coordinate_frames.csv
pohang_dataset_001/navigation/gps_corrected.txt
ins_gps_testing_platform/requirements.txt
ins_gps_testing_platform/scripts/ALTITUDE_ERROR_FIX_REPORT.md
ins_gps_testing_platform/scripts/copy_to_light_software.py
ins_gps_testing_platform/scripts/gps_elevation_analyzer.py
ins_gps_testing_platform/scripts/python_ahrs_visualizer.py
ins_gps_testing_platform/tests/__init__.py
ins_gps_testing_platform/tests/debug_vertical_coordinates.py
ins_gps_testing_platform/tests/test_altitude_error_fix.py
ins_gps_testing_platform/tests/test_baseline_loading_optimization.py
ins_gps_testing_platform/tests/test_complete_coordinate_fix.py
ins_gps_testing_platform/tests/test_coordinate_alignment.py
ins_gps_testing_platform/tests/test_coordinate_fix.py
ins_gps_testing_platform/tests/test_coordinate_fixes.py
ins_gps_testing_platform/tests/test_local_coordinates.py
ins_gps_testing_platform/tests/test_simple_alignment.py
ins_gps_testing_platform/tests/test_spatial_synchronization.py
ins_gps_testing_platform/tests/test_time_limiting.py
ins_gps_testing_platform/tests/test_visualization_enhancements.py
ins_gps_testing_platform/z_tests/__init__.py
ins_gps_testing_platform/z_tests/debug_vertical_coordinates.py
ins_gps_testing_platform/z_tests/test_altitude_error_fix.py
ins_gps_testing_platform/z_tests/test_baseline_loading_optimization.py
ins_gps_testing_platform/z_tests/test_complete_coordinate_fix.py
ins_gps_testing_platform/z_tests/test_coordinate_alignment.py
ins_gps_testing_platform/z_tests/test_coordinate_fix.py
ins_gps_testing_platform/z_tests/test_coordinate_fixes.py
ins_gps_testing_platform/z_tests/test_local_coordinates.py
ins_gps_testing_platform/z_tests/test_simple_alignment.py
ins_gps_testing_platform/z_tests/test_spatial_synchronization.py
ins_gps_testing_platform/z_tests/test_time_limiting.py
ins_gps_testing_platform/z_tests/test_visualization_enhancements.py
exports/pohang_results/trajectory_data.csv
/exports/pohang_results
exports/pohang_results/gps_data.csv
exports/pohang_results/coordinate_frames.csv
