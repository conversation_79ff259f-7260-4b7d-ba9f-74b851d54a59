"""Fusion Engine - 16-State INS Filter Integration and Management"""
import numpy as np
import logging
from dataclasses import dataclass
from typing import Dict, Optional, Tuple
# from fusion.ekf_28.saeid_gps_ins_ekf import Production<PERSON><PERSON>ilter, INSFilterConfig
from fusion.ekf_16.ins_filter_16state import ProductionINSFilter, INSFilterConfig
from config.configuration_manager import SensorConfig, TrajectoryConfig, EnvironmentConfig
from simulation.simulation_engine import SimulationData, SensorMeasurements
from preprocessing import MarineIMUPreprocessor
from utils.constants import DEFAULT_MAGNETIC_FIELD_NED

logger = logging.getLogger(__name__)

@dataclass
class EstimationResults:
    """INS filter estimation results - API """
    time: np.ndarray
    position: np.ndarray      # [N, 3] NED coordinates
    velocity: np.ndarray      # [N, 3] NED velocities  
    acceleration: np.ndarray  # [N, 3] NED accelerations (derived from IMU)
    orientation: np.ndarray   # [N, 4] quaternions [w, x, y, z]
    angular_velocity: np.ndarray  # [N, 3] body frame angular velocities (derived)
    sensor_biases: Dict[str, np.ndarray]  # Estimated sensor biases
    covariance_trace: np.ndarray  # Trace of covariance matrix over time

class FusionEngine:
    """Main fusion engine that integrates with 16-state ProductionINSFilter"""
    
    def __init__(self):
        self.ins_filter = None
        self.estimation_results = None
    
    def run_fusion(self, simulation_data: SimulationData,
                  sensor_config: SensorConfig,
                  trajectory_config: Optional[TrajectoryConfig] = None,
                  environment_config: Optional[EnvironmentConfig] = None,
                   lgf_reference: Optional[Tuple[float, float, float]] = None) -> EstimationResults:
        """
        Run complete sensor fusion using selective sensor participation
        
       

        Args:
            simulation_data: Complete simulation data with all sensors
            sensor_config: Configuration specifying which sensors to use
            trajectory_config: Trajectory configuration (optional, will use from simulation_data if not provided)
            environment_config: Environment configuration (optional, will use from simulation_data if not provided)
            lgf_reference: Local geodetic frame reference (optional)

        Returns:
            EstimationResults containing all estimates (API compatible)
        """
        logger.info("Starting 16-state sensor fusion...")

        # Get configurations - use provided ones or fall back to simulation data
        if trajectory_config is None:
            trajectory_config = simulation_data.configuration.get('trajectory')
        if environment_config is None:
            environment_config = simulation_data.configuration.get('environment')

        # Initialize 16-state filter
        self._initialize_filter(trajectory_config, sensor_config, environment_config, lgf_reference)

        # Run fusion with selective sensor participation
        results = self._process_sensor_measurements(
            simulation_data.sensor_measurements, sensor_config
        )

        logger.info("16-state sensor fusion completed successfully")
        return results
    
    def _initialize_filter(self, trajectory_config: TrajectoryConfig,
                          sensor_config: SensorConfig,
                          environment_config: Optional[EnvironmentConfig] = None,
                          lgf_reference: Optional[Tuple[float, float, float]] = None):
        """Initialize the 16-state INS filter"""

        # Use LGF reference if provided, otherwise fall back to trajectory config
        if lgf_reference is not None:
            ref_location = lgf_reference
            logger.info(f"Using LGF reference location: lat={ref_location[0]:.6f}, lon={ref_location[1]:.6f}, alt={ref_location[2]:.3f}")
        else:
            ref_location = (trajectory_config.center_lat, trajectory_config.center_lon, trajectory_config.center_alt)
            logger.info(f"Using trajectory config reference: lat={trajectory_config.center_lat}, lon={trajectory_config.center_lon}, alt={trajectory_config.center_alt}")

        # Create 16-state filter configuration based on sensor quality
        filter_config = self._create_filter_config(sensor_config)

        # Initialize 16-state filter
        self.ins_filter = ProductionINSFilter(ref_location, filter_config)

        # Set geomagnetic field (use environment config if available)
        if environment_config is not None:
            # Use magnetic field from environment config
            mag_field = np.array([
                environment_config.magnetic_field_strength * np.cos(np.radians(environment_config.magnetic_inclination)),
                environment_config.magnetic_field_strength * np.sin(np.radians(environment_config.magnetic_declination)),
                environment_config.magnetic_field_strength * np.sin(np.radians(environment_config.magnetic_inclination))
            ])
        else:
            # Use default magnetic field
            mag_field = np.array(DEFAULT_MAGNETIC_FIELD_NED)

        self.ins_filter.set_geomagnetic_field(mag_field)

        logger.info(f"16-state INS filter initialized with reference location: {ref_location}")
        logger.info(f"Magnetic field set to: {mag_field}")
    
    def _create_filter_config(self, sensor_config: SensorConfig) -> INSFilterConfig:
        """Create 16-state INS filter configuration from sensor configuration"""

        config = INSFilterConfig()

        # Use noise parameters directly from sensor configuration (loaded from JSON templates)
        config.acceleration_noise = sensor_config.accelerometer_noise
        config.angular_velocity_noise = sensor_config.gyroscope_noise

        # Use gyroscope bias process noise if available
        if hasattr(sensor_config, 'gyroscope_bias_process_noise') and sensor_config.gyroscope_bias_process_noise:
            config.gyroscope_bias_noise = sensor_config.gyroscope_bias_process_noise
            logger.info(f"Using gyroscope bias process noise: {sensor_config.gyroscope_bias_process_noise:.2e}")

        # Set process noise parameters optimized for 16-state filter
        config.position_noise = 0.1  # m/s (integrated from velocity uncertainty)
        config.velocity_noise = 0.1  # m/s²
        config.quaternion_noise = 1e-4  # rad/s (small for attitude stability)

        # Set initial covariance values appropriate for 16-state filter
        config.position_covariance = 100.0  # Large initial position uncertainty
        config.velocity_covariance = 10.0   # Moderate velocity uncertainty
        config.quaternion_covariance = 1e-1  # Moderate attitude uncertainty
        config.accelerometer_bias_covariance = 1e-2  # Accelerometer bias uncertainty
        config.gyroscope_bias_covariance = 1e-3     # Gyroscope bias uncertainty

        logger.info(f"16-state filter config: accel_noise={config.acceleration_noise}, gyro_noise={config.angular_velocity_noise}")
        return config
    
    def _process_sensor_measurements(self, sensor_measurements: SensorMeasurements,
                                   sensor_config: SensorConfig) -> EstimationResults:
        """Process sensor measurements with selective participation for 16-state filter"""

        # Initialize filter with first GPS reading if available for faster convergence
        if sensor_config.use_gps_position and len(sensor_measurements.gps_position) > 0:
            first_gps = sensor_measurements.gps_position[0]
            if not np.any(np.isnan(first_gps)):
                self.ins_filter.initialize_from_first_gps(first_gps)
                logger.info("16-state filter initialized with first GPS reading for faster convergence")

        # Get all unique time stamps and sort
        all_times = []
        
        if sensor_config.use_accelerometer:
            all_times.extend(sensor_measurements.time['accelerometer'])
        if sensor_config.use_gyroscope:
            all_times.extend(sensor_measurements.time['gyroscope'])
        if sensor_config.use_magnetometer:
            all_times.extend(sensor_measurements.time['magnetometer'])
        if sensor_config.use_gps_position:
            all_times.extend(sensor_measurements.time['gps_position'])
        if sensor_config.use_gps_velocity:
            all_times.extend(sensor_measurements.time['gps_velocity'])
        
        unique_times = np.unique(all_times)
        
        # Storage for results
        n_samples = len(unique_times)
        positions = np.zeros((n_samples, 3))
        velocities = np.zeros((n_samples, 3))
        accelerations = np.zeros((n_samples, 3))  # Derived from IMU
        orientations = np.zeros((n_samples, 4))
        angular_velocities = np.zeros((n_samples, 3))  # Derived from gyroscope
        covariance_traces = np.zeros(n_samples)
        
        # Create sensor data indices for efficient lookup
        sensor_indices = self._create_sensor_indices(sensor_measurements, sensor_config)
        
        # Process each time step
        prev_time = unique_times[0]
        
        for i, current_time in enumerate(unique_times):
            # Prediction step
            dt = current_time - prev_time if i > 0 else 0.01
            self.ins_filter.predict(dt)
            
            # Update steps (fuse available measurements)
            self._fuse_measurements_at_time(current_time, sensor_measurements, 
                                          sensor_config, sensor_indices)
            
            # Store results - 
            positions[i] = self.ins_filter.get_position_ned()
            velocities[i] = self.ins_filter.get_velocity_ned()
            accelerations[i] = self.ins_filter.get_acceleration_ned()  # Derived
            orientations[i] = self.ins_filter.get_orientation_quaternion()
            angular_velocities[i] = self.ins_filter.get_angular_velocity()  # Derived
            covariance_traces[i] = np.trace(self.ins_filter.get_covariance_matrix())
            
            prev_time = current_time
        
        # Get final sensor biases
        final_biases = self.ins_filter.get_sensor_biases()
        
        return EstimationResults(
            time=unique_times,
            position=positions,
            velocity=velocities,
            acceleration=accelerations,
            orientation=orientations,
            angular_velocity=angular_velocities,
            sensor_biases=final_biases,
            covariance_trace=covariance_traces
        )
    
    def _create_sensor_indices(self, sensor_measurements: SensorMeasurements,
                              sensor_config: SensorConfig) -> Dict:
        """Create efficient lookup indices for sensor data"""
        indices = {}
        
        if sensor_config.use_accelerometer:
            indices['accelerometer'] = {
                'times': sensor_measurements.time['accelerometer'],
                'data': sensor_measurements.accelerometer,
                'noise': sensor_config.accelerometer_noise**2
            }
        
        if sensor_config.use_gyroscope:
            indices['gyroscope'] = {
                'times': sensor_measurements.time['gyroscope'],
                'data': sensor_measurements.gyroscope,
                'noise': sensor_config.gyroscope_noise**2
            }
        
        if sensor_config.use_magnetometer:
            indices['magnetometer'] = {
                'times': sensor_measurements.time['magnetometer'],
                'data': sensor_measurements.magnetometer,
                'noise': sensor_config.magnetometer_noise**2
            }
        
        if sensor_config.use_gps_position:
            # Convert horizontal/vertical accuracy to 3D GPS noise [North_var, East_var, Down_var]
            if (hasattr(sensor_config, 'gps_horizontal_accuracy') and
                hasattr(sensor_config, 'gps_vertical_accuracy') and
                sensor_config.gps_horizontal_accuracy is not None and
                sensor_config.gps_vertical_accuracy is not None):
                # Create 3D noise: [North_variance, East_variance, Down_variance]
                gps_noise = [
                    sensor_config.gps_horizontal_accuracy**2,  # North variance
                    sensor_config.gps_horizontal_accuracy**2,  # East variance
                    sensor_config.gps_vertical_accuracy**2     # Down variance
                ]
            else:
                # Fallback: use scalar noise for all dimensions (should not happen with proper config)
                fallback_noise = getattr(sensor_config, 'gps_position_noise', 2.0)
                gps_noise = [fallback_noise**2, fallback_noise**2, fallback_noise**2]
                logger.warning("GPS horizontal/vertical accuracy not found, using fallback scalar noise")

            indices['gps_position'] = {
                'times': sensor_measurements.time['gps_position'],
                'data': sensor_measurements.gps_position,
                'noise': gps_noise
            }
        
        if sensor_config.use_gps_velocity:
            indices['gps_velocity'] = {
                'times': sensor_measurements.time['gps_velocity'],
                'data': sensor_measurements.gps_velocity,
                'noise': sensor_config.gps_velocity_noise**2
            }
        
        return indices
    
    def _fuse_measurements_at_time(self, current_time: float,
                                  sensor_measurements: SensorMeasurements,
                                  sensor_config: SensorConfig,
                                  sensor_indices: Dict):
        """Fuse all available measurements at current time for 16-state filter"""
        
        tolerance = 1e-6  # Time tolerance for finding measurements
        
        # Accelerometer - important for 16-state filter as it provides derived acceleration
        if 'accelerometer' in sensor_indices:
            data_info = sensor_indices['accelerometer']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_accelerometer(
                    data_info['data'][idx], data_info['noise']
                )
        
        # Gyroscope - important for 16-state filter as it provides derived angular velocity
        if 'gyroscope' in sensor_indices:
            data_info = sensor_indices['gyroscope']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_gyroscope(
                    data_info['data'][idx], data_info['noise']
                )
        
        # Magnetometer
        if 'magnetometer' in sensor_indices:
            data_info = sensor_indices['magnetometer']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_magnetometer(
                    data_info['data'][idx], data_info['noise']
                )
        
        # GPS Position
        if 'gps_position' in sensor_indices:
            data_info = sensor_indices['gps_position']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_gps_position(
                    data_info['data'][idx], data_info['noise']
                )
        
        # GPS Velocity
        if 'gps_velocity' in sensor_indices:
            data_info = sensor_indices['gps_velocity']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_gps_velocity(
                    data_info['data'][idx], data_info['noise']
                )
    
    def _find_time_index(self, target_time: float, time_array: np.ndarray, 
                        tolerance: float) -> Optional[int]:
        """Find index of closest time within tolerance"""
        if len(time_array) == 0:
            return None
        
        # Find closest time
        idx = np.argmin(np.abs(time_array - target_time))
        
        # Check if within tolerance
        if np.abs(time_array[idx] - target_time) <= tolerance:
            return idx
        
        return None