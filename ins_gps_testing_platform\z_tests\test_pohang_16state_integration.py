#!/usr/bin/env python3
"""
Test script to verify Pohang integration with 16-state fusion module
"""

import sys
from pathlib import Path

# Add parent directory to Python path to find pohang module
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

import numpy as np
import logging

def test_16state_import():
    """Test that 16-state fusion module can be imported"""
    print("Testing 16-state fusion module import...")
    
    try:
        from fusion.ekf_16.fusion_engine_16state import FusionEngine16State
        print("✅ 16-state fusion engine import successful")
        
        # Test instantiation
        engine = FusionEngine16State()
        print("✅ 16-state fusion engine instantiation successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_pohang_integration_import():
    """Test that Pohang integration can import 16-state fusion"""
    print("\nTesting Pohang integration with 16-state fusion...")
    
    try:
        from pohang.pohang_integration import PohangLidarProcessor
        print("✅ Pohang integration import successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_sensor_config_compatibility():
    """Test that sensor configuration has all required 16-state parameters"""
    print("\nTesting sensor configuration compatibility...")
    
    try:
        from config.configuration_manager import SensorConfig
        
        # Create sensor config
        config = SensorConfig()
        
        # Check for 16-state specific parameters
        required_params = [
            'accelerometer_bias_noise',
            'gyroscope_bias_noise', 
            'accelerometer_bias_covariance',
            'gyroscope_bias_covariance',
            'position_covariance',
            'velocity_covariance',
            'quaternion_covariance'
        ]
        
        missing_params = []
        for param in required_params:
            if not hasattr(config, param):
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ Missing parameters: {missing_params}")
            return False
        else:
            print("✅ All required 16-state parameters present")
            
            # Print parameter values
            print("Parameter values:")
            for param in required_params:
                value = getattr(config, param)
                print(f"  {param}: {value}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_template_compatibility():
    """Test that sensor templates have 16-state parameters"""
    print("\nTesting sensor template compatibility...")
    
    try:
        from config.configuration_manager import ConfigurationManager
        
        config_manager = ConfigurationManager()
        
        # Test loading navigation template
        config_manager.load_sensor_template("navigation")
        sensor_config = config_manager.current_config["sensors"]
        
        # Check for 16-state parameters
        required_params = [
            'accelerometer_bias_noise',
            'gyroscope_bias_noise',
            'accelerometer_bias_covariance',
            'gyroscope_bias_covariance',
            'position_covariance',
            'velocity_covariance',
            'quaternion_covariance'
        ]
        
        missing_params = []
        for param in required_params:
            if not hasattr(sensor_config, param):
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ Missing parameters in navigation template: {missing_params}")
            return False
        else:
            print("✅ Navigation template has all 16-state parameters")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all compatibility tests"""
    print("🧪 Testing Pohang Integration with 16-State Fusion Module")
    print("=" * 60)
    
    tests = [
        test_16state_import,
        test_pohang_integration_import,
        test_sensor_config_compatibility,
        test_template_compatibility
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All {total} tests passed!")
        print("🎉 Pohang integration is ready for 16-state fusion!")
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        print("🔧 Please fix the issues before using 16-state fusion with Pohang")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
