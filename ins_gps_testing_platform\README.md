# INS/GPS Fusion Testing Platform

Thes mini-software is designed and impleemetd as a testing platform for evaluating INS/GPS fusion algorithms in boat motion compensation applications.

In oeder to run this platform on your machine, you need to install the required packages. The required packages are listed in the `requirements.txt` file. A setup script is provided in the `z_setup` folder to automate the installation process (it has not been tested yet, so use it at your own risk). 


The rest of this file will provide more details abou the platfrom. 


Whithin this folder, a `setup.bat` file is provided for Windows users and a `setup.sh` file is provided for Linux and Mac users. The `setup.bat` file can be run by double-clicking on it. The `setup.sh` file can be run by opening a terminal and navigating to the `z_setup` folder and running the command `chmod +x setup.sh && ./setup.sh`. The `setup.sh` file can also be run by double-clicking on it. The framework has been originally and only tetsed on widnows environment.



## ######################
## This file has been prepared is such a way that be both human and AI freindly. ##
## ######################


## Framework Features

### 🎯 Complete Testing Suite
- **7 Trajectory Types**: Circular, Figure-8, Square, Straight, Spiral, Survey Lines, Coastal Patrol
- **3 IMU Quality Levels**: Consumer, Navigation, Survey grade sensors
- **5 Environment Conditions**: From ideal to extreme GPS interference scenarios
- **Selective Sensor Participation**: Enable/disable any sensor during estimation

### 🧮 Advanced Simulation
- **28-State Extended Kalman Filter**: Production-ready INS/GPS fusion
- **Complete Sensor Models**: Accelerometer, gyroscope, magnetometer, GPS position/velocity
- **Realistic Noise Models**: Based on actual sensor specifications
- **Configurable Sampling Rates**: Individual control for each sensor type

### 📊  Analysis
- **2D/3D Trajectory Visualization**: True vs estimated paths with GPS measurements
- **Error Analysis**: Position, velocity, orientation errors with some basic statistics ==> requires more development. 
- **Interactive Plots**: Zoom, pan, rotate, export


### 💾 Data Management
- **JSON Configuration**: Human-readable, version-controlled settings
- **Template System**: Pre-defined scenarios with auto-loading
- **Multiple Export Formats**: CSV, MATLAB, HDF5 for external analysis ==> requires more development and testing.
- **Industry Standards**: ==> reuires more development.

## Quick Start

### Installation

**Prerequisites**: Python 3.7 or higher

**Option 1: Automated Setup**
- **Windows**: Navigate to `z_setup/` and double-click `setup.bat`
- **Linux/Mac**: Run `cd z_setup && chmod +x setup.sh && ./setup.sh`

**Option 2: Manual Setup**
```bash
# Check environment and install dependencies:
cd z_setup
python setup_environment.py

# Or install manually:
pip install -r requirements.txt
```

**Run the Application**:
```bash
python main.py
```

### First Steps

1. **Configure Trajectory**: Choose from 7 trajectory types in Configuration tab
2. **Set Sensor Parameters**: Select IMU quality and individual sensor rates
3. **Choose Environment**: Set GPS accuracy and interference conditions
4. **Run Simulation**: Click "Run Simulation" to process complete scenario
5. **Analyze Results**: Review plots and statistics in Visualization tab

## System Architecture

```

┌─────────────────────────────────────────────────────────────────────────────────┐
│                           GUI Layer (Tkinter)                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│ Simulation │ Estimation │ Results &     │ Pohang       │ Pohang               │
│ Config     │ Control    │ Visualization │ Dataset      │ Report               │
│ Tab        │ Tab        │ Tab           │ Tab          │ Tab                  │
└─────────────────────────────────────────────────────────────────────────────────┘
                                         │
                                         ▼


┌─────────────────────────────────────────────────────────────────────────────────----------┐
│                          Core Processing Layer                                            │
├─────────────────────────────────────────────────────────────────────────          ────────┤
│ Simulation    │ Fusion        │ Analysis      │ Pohang Integration                        │
│ Engine        │ Engine        │ Engine        │ Module                                    │
│ - Trajectory  │ - 28-state    │ - Error       │ - Real Dataset Loading                    │
│   Generation  │   EKF Filter  │   Analysis    │ - Baseline Comparison                     │
│ - Sensor      │ - GPS/INS     │ - Statistics  │ - Coordinate Alignment                    │
│   Simulation  │   Integration │ - Report      │ - LiDAR Integration (Future)              │
│ - Noise       │ - Discrete    │   Generation  │ - Trajectory Synchronization(optional)    │
│   Models      │   Measurements│               │                                           │
└───────────────────────────────────────────────────────────────────────────────────────────┘
                                         │
                                         ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        Configuration & Data Layer                              │
├─────────────────────────────────────────────────────────────────────────────────┤
│ JSON Config   │ Template      │ Export        │ Real Dataset                    │
│ Management    │ System        │ Formats       │ Support                         │
│ - Trajectory  │ - Sensor      │ - CSV/MATLAB  │ - Pohang Canal Dataset          │
│ - Sensor      │   Presets     │ - ?HDF5       │ - AHRS/GPS/Baseline Data        │
│ - Environment │ - Environment │ - JSON        │ - Time Synchronization          │
│   Settings    │   Templates   │               │ - Coordinate Transformations    │
└─────────────────────────────────────────────────────────────────────────────────┘


```

## Configuration Examples

### Circular Trajectory - Navigation Grade
```json
{
  "trajectory": {
    "trajectory_type": "CIRCULAR",
    "duration": 60.0,
    "vessel_speed": 5.0,
    "vessel_length": 20.0,
    "center_lat": 42.3601,
    "center_lon": -71.0589,
    "center_alt": 0.0,
    "radius": 100.0
  },
  "sensors": {
    "imu_quality": "NAVIGATION",
    "accelerometer_rate": 100.0,
    "gps_position_rate": 10.0
  },
  "environment": {
    "condition_template": "GOOD",
    "gps_horizontal_accuracy": 2.0
  }
}
```

### Survey Mission - High Precision
```json
{
  "trajectory": {
    "trajectory_type": "SURVEY_LINES",
    "vessel_speed": 4.0,
    "vessel_length": 20.0,
    "center_lat": 42.3601,
    "center_lon": -71.0589,
    "center_alt": 0.0,
    "survey_spacing": 50.0,
    "survey_lines": 5
  },
  "sensors": {
    "imu_quality": "SURVEY",
    "accelerometer_rate": 200.0
  },
  "environment": {
    "condition_template": "IDEAL"
  }
}
```

## Trajectory Types example

| Type | Description | Parameters |
|------|-------------|------------|
| **CIRCULAR** | Constant radius circular path | radius, speed |
| **FIGURE8** | Figure-8 pattern (lemniscate) | width, height |
| **SQUARE** | Square with sharp corners | size |
| **STRAIGHT** | Straight line motion | speed, duration |
| **SPIRAL** | Expanding/contracting spiral | turns, pitch |
| **SURVEY_LINES** | Lawnmower survey pattern | spacing, lines |
| **COASTAL_PATROL** | Irregular coastal patrol | waypoints, irregularity |

## Sensor Quality Specifications example

| Quality | Accelerometer | Gyroscope | Magnetometer | Applications |
|---------|---------------|-----------|--------------|--------------|
| **Consumer** | 1.0 m/s² | 0.1 rad/s | 2.0 µT | Smartphones, basic navigation |
| **Navigation** | 0.1 m/s² | 0.01 rad/s | 0.5 µT | Automotive, marine systems |
| **Survey** | 0.01 m/s² | 0.001 rad/s | 0.1 µT | High-precision applications |

## Error Analysis Metrics examples

- **RMS Position Error**: Root-mean-square 2D position accuracy
- **Maximum Position Error**: Worst-case position deviation
- **95th Percentile Error**: Statistical confidence metric
- **Velocity Error**: Speed and direction accuracy
- **Orientation Error**: Heading and attitude accuracy

## File Structure

```
ins_gps_testing_platform/
├── main.py                    # Application entry point
├── README.md                  # Project documentation
│
│
├── config/                    # Configuration management
│   ├── configuration_manager.py
│   └── templates/            # JSON configuration templates
├── gui/                      # User interface components
│   ├── main_gui.py           # Main application window
│   ├── configuration_tab.py  # Simulation configuration
│   ├── estimation_tab.py     # Estimation control
│   ├── visualization_tab.py  # Results visualization
│   ├── pohang_tab.py         # Pohang dataset processing
│   ├── pohang_results_tab.py # Pohang results viewer
│   └── widgets/              # Custom GUI widgets
├── simulation/               # Trajectory and sensor simulation
│   └── simulation_engine.py
├── fusion/                   # INS filter integration
│   ├── ekf_28/              # 28-state EKF implementation
│   │   ├── fusion_engine.py
│   │   └── saeid_gps_ins_ekf.py
│   └── ekf_16/              # 16-state EKF implementation
│       ├── fusion_engine_16state.py
│       └── ins_filter_16state.py
├── analysis/                 # Error analysis and statistics
│   └── analysis_engine.py
├── pohang/                   # Real dataset integration
│   ├── pohang_integration.py # Main integration module
│   ├── pohang_analizor.py    # Dataset loader and processor
│   ├── trajectory_comparison.py # Trajectory alignment & comparison
│   ├── lidar_georeferencing.py # LiDAR processing (future)
│   ├── pohang_visualizor.py  # Visualization utilities
│   └── README.md             # Pohang module documentation
├── preprocessing/            # Data preprocessing utilities
│   └── marine_imu_preprocessor.py
├── data/                     # Data models and structures
│   └── data_models.py
├── utils/                    # Utilities and helpers
│   ├── constants.py
│   ├── helpers.py
│   └── coordinate_transforms.py
├── z_setup/                  # Environment setup tools
│   ├── setup_environment.py  # Automated environment setup
│   ├── setup.bat             # Windows setup script
│   ├── setup.sh              # Linux/Mac setup script
│   └── requirements.txt      # Setup-specific requirements
├── z_scripts/                # Utility and analysis scripts
│   ├── gps_elevation_analyzer.py
│   ├── python_ahrs_visualizer.py
│   └── copy_to_light_software.py
├── z_tests/                  # Development and testing
│   ├── test_import.py
│   ├── test_spatial_synchronization.py
│   ├── test_coordinate_alignment.py
│   └── [various test files]
├── z_test_outputs/           # Test result outputs
│   └── [generated test plots and reports]
└── exports/                  # Generated results and plots
    └── [simulation and analysis outputs]
```

## Technical Details

### INS Filter Specifications
- **28-State Extended Kalman Filter**: Complete implementation
- **State Vector**: Position, velocity, acceleration, orientation, angular velocity, sensor biases, magnetic field
- **Measurement Models**: Accelerometer, gyroscope, magnetometer, GPS position/velocity
- **Coordinate Frames**: NED navigation frame, body-fixed frame
- **Geodetic Transforms**: WGS84 ellipsoid model

### Numerical Methods
- **Quaternion Kinematics**: Exact integration using matrix exponential
- **Covariance Propagation**: Joseph form for numerical stability
- **Sensor Fusion**: Selective participation without regenerating data
- **Error Computation**: Proper statistical analysis with confidence intervals


### Considerations and Performance Tips
 - The memory efficienty was not a priority at the moment. 
 - Slow Performance**: Reduce trajectory duration or sampling rate for faster processing
 - Memory Issues**: Large simulations may require more RAM; consider shorter durations

- **Sensor Participation**: Disable unused sensors to speed up processing
- **Visualization**: Use "Clear Results" between runs to free memory
- 



## Current License
This software is provided as-is for research and development purposes in motion compensation applications in Laval university, Envisining Lab and other collaborators.
