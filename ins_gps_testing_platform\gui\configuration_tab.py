"""Simulation Tab - Trajectory, Sensor, and Environment Setup with Data Export"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import logging
from config.configuration_manager import ConfigurationManager
from utils.helpers import DataPersistenceManager

logger = logging.getLogger(__name__)

class SimulationTab:
    """Simulation tab for trajectory setup and data generation with export functionality"""

    def __init__(self, parent, config_manager: ConfigurationManager, main_gui):
        self.parent = parent
        self.config_manager = config_manager
        self.main_gui = main_gui

        # Initialize data persistence manager
        self.data_manager = DataPersistenceManager()

        # Store simulation data for export
        self.simulation_data = None

        # Create main frame
        self.frame = ttk.Frame(parent)

        # Create UI components
        self.create_trajectory_section()
        self.create_sensor_section()
        self.create_environment_section()
        self.create_control_buttons()

        logger.info("Simulation tab initialized")
    
    def create_trajectory_section(self):
        """Create trajectory configuration section"""
        # Trajectory frame
        traj_frame = ttk.LabelFrame(self.frame, text="Trajectory Configuration")
        traj_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Trajectory type selection
        ttk.Label(traj_frame, text="Trajectory Type:").grid(row=0, column=0, sticky=tk.W)
        self.traj_type_var = tk.StringVar(value="CIRCULAR")
        traj_combo = ttk.Combobox(traj_frame, textvariable=self.traj_type_var,
                                 values=["CIRCULAR", "FIGURE8", "SQUARE", "STRAIGHT", 
                                        "SPIRAL", "SURVEY_LINES", "COASTAL_PATROL"])
        traj_combo.grid(row=0, column=1, sticky=tk.W)
        traj_combo.bind("<<ComboboxSelected>>", self.on_trajectory_type_changed)
        
        # Load template button
        ttk.Button(traj_frame, text="Load Template", 
                  command=self.load_trajectory_template).grid(row=0, column=2, padx=5)
        
        # Duration and sampling
        ttk.Label(traj_frame, text="Duration (s):").grid(row=1, column=0, sticky=tk.W)
        self.duration_var = tk.DoubleVar(value=60.0)
        duration_spinbox = tk.Spinbox(traj_frame, textvariable=self.duration_var,
                                     from_=10, to=3600, increment=10, width=10)
        duration_spinbox.grid(row=1, column=1, sticky=tk.W)
        
        ttk.Label(traj_frame, text="Sampling Rate (Hz):").grid(row=2, column=0, sticky=tk.W)
        self.sampling_rate_var = tk.DoubleVar(value=100.0)
        sampling_spinbox = tk.Spinbox(traj_frame, textvariable=self.sampling_rate_var,
                                     from_=1, to=1000, increment=10, width=10)
        sampling_spinbox.grid(row=2, column=1, sticky=tk.W)

        # Common vessel parameters
        ttk.Label(traj_frame, text="Vessel Length (m):").grid(row=3, column=0, sticky=tk.W)
        self.vessel_length_var = tk.DoubleVar(value=20.0)
        vessel_length_spinbox = tk.Spinbox(traj_frame, textvariable=self.vessel_length_var,
                                          from_=5, to=200, increment=5, width=10)
        vessel_length_spinbox.grid(row=3, column=1, sticky=tk.W)

        ttk.Label(traj_frame, text="Vessel Speed (m/s):").grid(row=3, column=2, sticky=tk.W)
        self.vessel_speed_var = tk.DoubleVar(value=5.0)
        vessel_speed_spinbox = tk.Spinbox(traj_frame, textvariable=self.vessel_speed_var,
                                         from_=0.5, to=20.0, increment=0.5, width=10)
        vessel_speed_spinbox.grid(row=3, column=3, sticky=tk.W)

        # Trajectory-specific parameters frame
        self.traj_params_frame = ttk.Frame(traj_frame)
        self.traj_params_frame.grid(row=4, column=0, columnspan=4, sticky=tk.W+tk.E, pady=5)
        
        # LGF (Local Geodetic Frame) reference location
        ttk.Label(traj_frame, text="LGF Ref Lat (deg):").grid(row=5, column=0, sticky=tk.W)
        self.center_lat_var = tk.DoubleVar(value=42.3601)
        lat_spinbox = tk.Spinbox(traj_frame, textvariable=self.center_lat_var,
                                from_=-90, to=90, increment=0.1, width=10, format="%.4f")
        lat_spinbox.grid(row=5, column=1, sticky=tk.W)

        ttk.Label(traj_frame, text="LGF Ref Lon (deg):").grid(row=6, column=0, sticky=tk.W)
        self.center_lon_var = tk.DoubleVar(value=-71.0589)
        lon_spinbox = tk.Spinbox(traj_frame, textvariable=self.center_lon_var,
                                from_=-180, to=180, increment=0.1, width=10, format="%.4f")
        lon_spinbox.grid(row=6, column=1, sticky=tk.W)
        
        # Create initial trajectory parameters
        self.create_trajectory_parameters()
    
    def create_trajectory_parameters(self):
        """Create trajectory-specific parameter widgets"""
        # Clear existing parameters
        for widget in self.traj_params_frame.winfo_children():
            widget.destroy()
        
        traj_type = self.traj_type_var.get()
        
        if traj_type == "CIRCULAR":
            ttk.Label(self.traj_params_frame, text="Radius (m):").grid(row=0, column=0, sticky=tk.W)
            self.radius_var = tk.DoubleVar(value=100.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.radius_var,
                      from_=10, to=1000, increment=10, width=10).grid(row=0, column=1)
        
        elif traj_type == "FIGURE8":
            ttk.Label(self.traj_params_frame, text="Width (m):").grid(row=0, column=0, sticky=tk.W)
            self.fig8_width_var = tk.DoubleVar(value=200.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.fig8_width_var,
                      from_=50, to=1000, increment=25, width=10).grid(row=0, column=1)
            
            ttk.Label(self.traj_params_frame, text="Height (m):").grid(row=0, column=2, sticky=tk.W)
            self.fig8_height_var = tk.DoubleVar(value=100.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.fig8_height_var,
                      from_=25, to=500, increment=25, width=10).grid(row=0, column=3)
        
        elif traj_type == "SQUARE":
            ttk.Label(self.traj_params_frame, text="Size (m):").grid(row=0, column=0, sticky=tk.W)
            self.square_size_var = tk.DoubleVar(value=200.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.square_size_var,
                      from_=50, to=1000, increment=25, width=10).grid(row=0, column=1)

            ttk.Label(self.traj_params_frame, text="Turn Radius Factor:").grid(row=0, column=2, sticky=tk.W)
            self.min_turn_radius_factor_var = tk.DoubleVar(value=3.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.min_turn_radius_factor_var,
                      from_=1.5, to=10.0, increment=0.5, width=10).grid(row=0, column=3)
        
        elif traj_type == "SPIRAL":
            ttk.Label(self.traj_params_frame, text="Turns:").grid(row=0, column=0, sticky=tk.W)
            self.spiral_turns_var = tk.IntVar(value=3)
            tk.Spinbox(self.traj_params_frame, textvariable=self.spiral_turns_var,
                      from_=1, to=10, increment=1, width=10).grid(row=0, column=1)
            
            ttk.Label(self.traj_params_frame, text="Pitch (m):").grid(row=0, column=2, sticky=tk.W)
            self.spiral_pitch_var = tk.DoubleVar(value=50.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.spiral_pitch_var,
                      from_=10, to=200, increment=10, width=10).grid(row=0, column=3)
        
        elif traj_type == "SURVEY_LINES":
            ttk.Label(self.traj_params_frame, text="Spacing (m):").grid(row=0, column=0, sticky=tk.W)
            self.survey_spacing_var = tk.DoubleVar(value=50.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.survey_spacing_var,
                      from_=10, to=200, increment=10, width=10).grid(row=0, column=1)
            
            ttk.Label(self.traj_params_frame, text="Lines:").grid(row=0, column=2, sticky=tk.W)
            self.survey_lines_var = tk.IntVar(value=5)
            tk.Spinbox(self.traj_params_frame, textvariable=self.survey_lines_var,
                      from_=2, to=20, increment=1, width=10).grid(row=0, column=3)
    
    def create_sensor_section(self):
        """Create sensor configuration section"""
        # Main sensor frame
        sensor_frame = ttk.LabelFrame(self.frame, text="Sensor Configuration")
        sensor_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Quality template
        ttk.Label(sensor_frame, text="IMU Quality:").grid(row=0, column=0, sticky=tk.W)
        self.imu_quality_var = tk.StringVar(value="NAVIGATION")
        quality_combo = ttk.Combobox(sensor_frame, textvariable=self.imu_quality_var,
                                    values=["CONSUMER", "NAVIGATION", "SURVEY"])
        quality_combo.grid(row=0, column=1, sticky=tk.W)
        quality_combo.bind("<<ComboboxSelected>>", self.on_sensor_quality_changed)
        
        ttk.Button(sensor_frame, text="Load Template", 
                  command=self.load_sensor_template).grid(row=0, column=2, padx=5)
        
        # Individual sensor rates
        rates_frame = ttk.LabelFrame(sensor_frame, text="Sensor Rates (Hz)")
        rates_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        ttk.Label(rates_frame, text="Accelerometer:").grid(row=0, column=0, sticky=tk.W)
        self.accel_rate_var = tk.DoubleVar(value=100.0)
        tk.Spinbox(rates_frame, textvariable=self.accel_rate_var,
                  from_=1, to=1000, increment=10, width=8).grid(row=0, column=1)
        
        ttk.Label(rates_frame, text="Gyroscope:").grid(row=0, column=2, sticky=tk.W)
        self.gyro_rate_var = tk.DoubleVar(value=100.0)
        tk.Spinbox(rates_frame, textvariable=self.gyro_rate_var,
                  from_=1, to=1000, increment=10, width=8).grid(row=0, column=3)
        
        ttk.Label(rates_frame, text="Magnetometer:").grid(row=1, column=0, sticky=tk.W)
        self.mag_rate_var = tk.DoubleVar(value=20.0)
        tk.Spinbox(rates_frame, textvariable=self.mag_rate_var,
                  from_=1, to=100, increment=5, width=8).grid(row=1, column=1)
        
        ttk.Label(rates_frame, text="GPS Position:").grid(row=1, column=2, sticky=tk.W)
        self.gps_pos_rate_var = tk.DoubleVar(value=10.0)
        tk.Spinbox(rates_frame, textvariable=self.gps_pos_rate_var,
                  from_=0.1, to=100, increment=1, width=8).grid(row=1, column=3)
        
        # Sensor participation
        participation_frame = ttk.LabelFrame(sensor_frame, text="Sensor Participation")
        participation_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        self.use_accel_var = tk.BooleanVar(value=True)
        self.use_gyro_var = tk.BooleanVar(value=True)
        self.use_mag_var = tk.BooleanVar(value=True)
        self.use_gps_pos_var = tk.BooleanVar(value=True)
        self.use_gps_vel_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(participation_frame, text="Accelerometer", 
                       variable=self.use_accel_var).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(participation_frame, text="Gyroscope", 
                       variable=self.use_gyro_var).grid(row=0, column=1, sticky=tk.W)
        ttk.Checkbutton(participation_frame, text="Magnetometer", 
                       variable=self.use_mag_var).grid(row=0, column=2, sticky=tk.W)
        ttk.Checkbutton(participation_frame, text="GPS Position", 
                       variable=self.use_gps_pos_var).grid(row=1, column=0, sticky=tk.W)
        ttk.Checkbutton(participation_frame, text="GPS Velocity",
                       variable=self.use_gps_vel_var).grid(row=1, column=1, sticky=tk.W)

        # Gyroscope specifications
        gyro_frame = ttk.LabelFrame(sensor_frame, text="Gyroscope Specifications")
        gyro_frame.grid(row=3, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)

        ttk.Label(gyro_frame, text="Angle Random Walk (deg/√hr):").grid(row=0, column=0, sticky=tk.W)
        self.gyro_arw_var = tk.DoubleVar(value=0.003)
        tk.Spinbox(gyro_frame, textvariable=self.gyro_arw_var,
                  from_=0.001, to=1.0, increment=0.001, width=10, format="%.3f").grid(row=0, column=1, sticky=tk.W)

        ttk.Label(gyro_frame, text="Bias Stability (deg/hr):").grid(row=0, column=2, sticky=tk.W)
        self.gyro_bias_var = tk.DoubleVar(value=0.0065)
        tk.Spinbox(gyro_frame, textvariable=self.gyro_bias_var,
                  from_=0.001, to=1.0, increment=0.001, width=10, format="%.4f").grid(row=0, column=3, sticky=tk.W)

    def create_environment_section(self):
        """Create environment configuration section"""
        env_frame = ttk.LabelFrame(self.frame, text="Environmental Conditions")
        env_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Condition template
        ttk.Label(env_frame, text="Condition:").grid(row=0, column=0, sticky=tk.W)
        self.env_condition_var = tk.StringVar(value="GOOD")
        condition_combo = ttk.Combobox(env_frame, textvariable=self.env_condition_var,
                                      values=["IDEAL", "GOOD", "MODERATE", "POOR", "EXTREME"])
        condition_combo.grid(row=0, column=1, sticky=tk.W)
        condition_combo.bind("<<ComboboxSelected>>", self.on_environment_changed)
        
        ttk.Button(env_frame, text="Load Template", 
                  command=self.load_environment_template).grid(row=0, column=2, padx=5)
        
        # GPS accuracy controls
        gps_frame = ttk.LabelFrame(env_frame, text="GPS Accuracy (meters)")
        gps_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        ttk.Label(gps_frame, text="Horizontal:").grid(row=0, column=0, sticky=tk.W)
        self.gps_horiz_var = tk.DoubleVar(value=2.0)
        tk.Spinbox(gps_frame, textvariable=self.gps_horiz_var,
                  from_=0.1, to=50, increment=0.5, width=8).grid(row=0, column=1)
        
        ttk.Label(gps_frame, text="Vertical:").grid(row=0, column=2, sticky=tk.W)
        self.gps_vert_var = tk.DoubleVar(value=3.0)
        tk.Spinbox(gps_frame, textvariable=self.gps_vert_var,
                  from_=0.1, to=100, increment=1, width=8).grid(row=0, column=3)
        
        ttk.Label(gps_frame, text="Velocity:").grid(row=1, column=0, sticky=tk.W)
        self.gps_vel_var = tk.DoubleVar(value=0.1)
        tk.Spinbox(gps_frame, textvariable=self.gps_vel_var,
                  from_=0.01, to=5, increment=0.05, width=8).grid(row=1, column=1)
    
    def create_control_buttons(self):
        """Create control buttons"""
        button_frame = ttk.Frame(self.frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)

        # Simulation controls
        sim_frame = ttk.LabelFrame(button_frame, text="Simulation Controls")
        sim_frame.pack(fill=tk.X, pady=5)

        ttk.Button(sim_frame, text="Generate Simulation Data",
                  command=self.run_simulation_only).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(sim_frame, text="Show Ground Truth",
                  command=self.show_ground_truth).pack(side=tk.LEFT, padx=5, pady=5)

        # Export controls
        export_frame = ttk.LabelFrame(button_frame, text="Export Controls")
        export_frame.pack(fill=tk.X, pady=5)

        ttk.Button(export_frame, text="Export Simulation Data",
                  command=self.export_simulation_data).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(export_frame, text="Save Configuration",
                  command=self.save_current_config).pack(side=tk.LEFT, padx=5, pady=5)

        # Utility controls - COMMENTED OUT AS REQUESTED
        # util_frame = ttk.LabelFrame(button_frame, text="Utility Controls")
        # util_frame.pack(fill=tk.X, pady=5)

        # ttk.Button(util_frame, text="Reset to Defaults",
        #           command=self.reset_to_defaults).pack(side=tk.LEFT, padx=5, pady=5)
        # ttk.Button(util_frame, text="Load Configuration",
        #           command=self.load_configuration).pack(side=tk.LEFT, padx=5, pady=5)
    
    def on_trajectory_type_changed(self, event=None):
        """Handle trajectory type change"""
        self.create_trajectory_parameters()
        self.update_configuration()
    
    def on_sensor_quality_changed(self, event=None):
        """Handle sensor quality change"""
        self.config_manager.update_sensor_quality(self.imu_quality_var.get())
    
    def on_environment_changed(self, event=None):
        """Handle environment condition change"""
        self.config_manager.update_environment_quality(self.env_condition_var.get())
        self.refresh_environment_ui()
    
    def load_trajectory_template(self):
        """Load trajectory template"""
        template_name = self.traj_type_var.get()
        self.config_manager.load_trajectory_template(template_name)
        self.refresh_trajectory_ui()
    
    def load_sensor_template(self):
        """Load sensor template"""
        template_name = self.imu_quality_var.get()
        self.config_manager.load_sensor_template(template_name)
        self.refresh_sensor_ui()
    
    def load_environment_template(self):
        """Load environment template"""
        template_name = self.env_condition_var.get()
        self.config_manager.load_environment_template(template_name)
        self.refresh_environment_ui()
    
    def refresh_trajectory_ui(self):
        """Refresh trajectory UI with current configuration"""
        config = self.config_manager.get_trajectory_config()
        self.duration_var.set(config.duration)
        self.sampling_rate_var.set(config.sampling_rate)
        self.vessel_length_var.set(config.vessel_length)
        self.vessel_speed_var.set(config.vessel_speed)
        self.center_lat_var.set(config.center_lat)
        self.center_lon_var.set(config.center_lon)

        # Set trajectory-specific parameters
        if hasattr(self, 'radius_var'):
            self.radius_var.set(config.radius)
        if hasattr(self, 'min_turn_radius_factor_var'):
            self.min_turn_radius_factor_var.set(config.min_turn_radius_factor)
    
    def refresh_sensor_ui(self):
        """Refresh sensor UI with current configuration"""
        config = self.config_manager.get_sensor_config()
        self.accel_rate_var.set(config.accelerometer_rate)
        self.gyro_rate_var.set(config.gyroscope_rate)
        self.mag_rate_var.set(config.magnetometer_rate)
        self.gps_pos_rate_var.set(config.gps_position_rate)
        
        self.use_accel_var.set(config.use_accelerometer)
        self.use_gyro_var.set(config.use_gyroscope)
        self.use_mag_var.set(config.use_magnetometer)
        self.use_gps_pos_var.set(config.use_gps_position)
        self.use_gps_vel_var.set(config.use_gps_velocity)

        # Gyroscope specifications
        self.gyro_arw_var.set(getattr(config, 'gyroscope_arw', 0.003))
        self.gyro_bias_var.set(getattr(config, 'gyroscope_bias_stability', 0.0065))
    
    def refresh_environment_ui(self):
        """Refresh environment UI with current configuration"""
        config = self.config_manager.get_environment_config()
        self.gps_horiz_var.set(config.gps_horizontal_accuracy)
        self.gps_vert_var.set(config.gps_vertical_accuracy)
        self.gps_vel_var.set(config.gps_velocity_accuracy)
    
    def refresh_ui(self):
        """Refresh entire UI with current configuration"""
        self.refresh_trajectory_ui()
        self.refresh_sensor_ui()
        self.refresh_environment_ui()
    
    def update_configuration(self):
        """Update configuration manager with current UI values"""
        # Update trajectory config
        traj_config = self.config_manager.get_trajectory_config()
        traj_config.trajectory_type = self.traj_type_var.get()
        traj_config.duration = self.duration_var.get()
        traj_config.sampling_rate = self.sampling_rate_var.get()
        traj_config.vessel_length = self.vessel_length_var.get()
        traj_config.vessel_speed = self.vessel_speed_var.get()
        traj_config.center_lat = self.center_lat_var.get()
        traj_config.center_lon = self.center_lon_var.get()
        
        # Update sensor config  
        sensor_config = self.config_manager.get_sensor_config()
        sensor_config.accelerometer_rate = self.accel_rate_var.get()
        sensor_config.gyroscope_rate = self.gyro_rate_var.get()
        sensor_config.magnetometer_rate = self.mag_rate_var.get()
        sensor_config.gps_position_rate = self.gps_pos_rate_var.get()
        
        sensor_config.use_accelerometer = self.use_accel_var.get()
        sensor_config.use_gyroscope = self.use_gyro_var.get()
        sensor_config.use_magnetometer = self.use_mag_var.get()
        sensor_config.use_gps_position = self.use_gps_pos_var.get()
        sensor_config.use_gps_velocity = self.use_gps_vel_var.get()

        # Update gyroscope specifications and recalculate noise
        self.config_manager.update_gyroscope_specs(
            self.gyro_arw_var.get(),
            self.gyro_bias_var.get()
        )
        
        # Update environment config
        env_config = self.config_manager.get_environment_config()
        env_config.gps_horizontal_accuracy = self.gps_horiz_var.get()
        env_config.gps_vertical_accuracy = self.gps_vert_var.get()
        env_config.gps_velocity_accuracy = self.gps_vel_var.get()
    
    def save_current_config(self):
        """Save current configuration"""
        self.update_configuration()
        self.main_gui.save_configuration()
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        result = messagebox.askyesno("Reset Configuration",
                                   "Reset all settings to defaults?")
        if result:
            self.config_manager = ConfigurationManager()
            self.refresh_ui()
            self.main_gui.update_status("Configuration reset to defaults")

    def run_simulation_only(self):
        """Run simulation without estimation - for data generation only"""
        try:
            self.main_gui.update_status("Generating simulation data...")
            self.main_gui.update_progress(0)

            # Update configuration from UI
            self.update_configuration()

            # Get current configuration
            traj_config = self.config_manager.get_trajectory_config()
            sensor_config = self.config_manager.get_sensor_config()
            env_config = self.config_manager.get_environment_config()

            # Run simulation only
            self.main_gui.update_progress(50)
            self.simulation_data = self.main_gui.simulation_engine.run_simulation(
                traj_config, sensor_config, env_config
            )

            self.main_gui.update_progress(100)
            self.main_gui.update_status("Simulation data generated successfully")

            # Enable export button
            messagebox.showinfo("Success",
                              "Simulation data generated successfully!\n"
                              "user can now export the data and configuration.")

        except Exception as e:
            logger.error(f"Simulation failed: {e}")
            messagebox.showerror("Simulation Error", f"Simulation failed:\n{str(e)}")
            self.main_gui.update_status("Simulation failed")
            self.main_gui.update_progress(0)

    def show_ground_truth(self):
        """Show ground truth trajectory visualization"""
        if self.simulation_data is None:
            messagebox.showwarning("No Data", "No simulation data available. Run simulation first.")
            return

        # Switch to visualization tab and show ground truth
        self.main_gui.viz_tab.show_ground_truth_only(self.simulation_data.ground_truth)
        self.main_gui.notebook.select(2)  # Visualization is tab 2

    def export_simulation_data(self):
        """Export simulation data and configuration to files"""
        if self.simulation_data is None:
            messagebox.showwarning("No Data", "No simulation data to export. Run simulation first.")
            return

        # Get scenario name from user
        scenario_name = simpledialog.askstring(
            "Export Data",
            "Enter scenario name:",
            initialvalue="simulation_scenario"
        )

        if not scenario_name:
            return

        try:
            # Create scenario folder
            scenario_path = self.data_manager.create_scenario_folder(scenario_name)

            # Save simulation data
            data_saved = self.data_manager.save_simulation_data(
                self.simulation_data, scenario_path
            )

            # Save estimation-optimized configuration
            config_dict = {
                'trajectory': self.config_manager.get_trajectory_config(),
                'sensors': self.config_manager.get_sensor_config(),
                'environment': self.config_manager.get_environment_config()
            }

            config_saved = self.data_manager.save_configuration(
                config_dict, scenario_path
            )

            if data_saved and config_saved:
                messagebox.showinfo("Export Success",
                                  f"Data and configuration exported to:\n{scenario_path}")
                self.main_gui.update_status(f"Data exported to {scenario_path}")
            else:
                messagebox.showerror("Export Error", "Failed to export some files")

        except Exception as e:
            logger.error(f"Export failed: {e}")
            messagebox.showerror("Export Error", f"Export failed:\n{str(e)}")

    def load_configuration(self):
        """Load configuration from file"""
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            defaultextension=".json"
        )
        if filename:
            try:
                import os
                # Extract just the filename without extension
                config_name = os.path.splitext(os.path.basename(filename))[0]
                if self.config_manager.load_configuration(config_name):
                    self.refresh_ui()
                    self.main_gui.update_status(f"Configuration loaded: {config_name}")
                else:
                    messagebox.showerror("Error", "Failed to load configuration")
            except Exception as e:
                logger.error(f"Failed to load configuration: {e}")
                messagebox.showerror("Error", f"Failed to load configuration:\n{str(e)}")
