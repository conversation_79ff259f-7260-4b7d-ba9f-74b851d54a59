#!/usr/bin/env python3
"""
INS/GPS Fusion Testing Platform - Complete Project Generator

This script generates the complete INS/GPS testing platform project
with all modules, GUI, configuration templates, and functionality.

Run once to create the entire production-ready system.
"""

import os
import json
from pathlib import Path

def create_project_structure():
    """Create the complete project directory structure and all files"""
    
    # Define project structure
    project_name = "ins_gps_testing_platform"
    
    # Create main project directory
    os.makedirs(project_name, exist_ok=True)
    os.chdir(project_name)
    
    # Create all subdirectories
    directories = [
        "config", "config/templates", "config/templates/trajectories",
        "config/templates/sensors", "config/templates/environments",
        "gui", "gui/widgets", "simulation", "fusion", "analysis", 
        "data", "utils", "tests", "exports"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print(f"Created project structure: {project_name}/")
    
    # Generate all files
    generate_main_entry_point()
    generate_requirements_file()
    generate_configuration_system()
    generate_gui_system()
    generate_simulation_engine()
    generate_fusion_engine()
    generate_analysis_engine()
    generate_data_models()
    generate_utility_modules()
    generate_configuration_templates()
    generate_init_files()
    
    print("✅ Complete INS/GPS Testing Platform generated successfully!")
    print(f"📁 Project location: {os.getcwd()}")
    print("🚀 To run: python main.py")
    print("📦 Install dependencies: pip install -r requirements.txt")

def generate_main_entry_point():
    """Generate main.py - Application entry point"""
    content = '''#!/usr/bin/env python3
"""
INS/GPS Fusion Testing Platform - Main Entry Point
Production-ready testing platform for boat motion compensation evaluation
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.main_gui import MainGUI
import tkinter as tk
from tkinter import messagebox
import logging

def setup_logging():
    """Setup application logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('ins_gps_platform.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """Main application entry point"""
    try:
        # Setup logging
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("Starting INS/GPS Testing Platform...")
        
        # Create main GUI
        root = tk.Tk()
        app = MainGUI(root)
        
        # Start GUI event loop
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("Application Error", f"Failed to start application:\\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    with open("main.py", "w") as f:
        f.write(content)

def generate_requirements_file():
    """Generate requirements.txt"""
    content = '''# INS/GPS Testing Platform Dependencies
numpy>=1.21.0
scipy>=1.7.0
matplotlib>=3.5.0
pandas>=1.3.0
h5py>=3.6.0
plotly>=5.0.0
'''
    with open("requirements.txt", "w") as f:
        f.write(content)

def generate_configuration_system():
    """Generate complete configuration management system"""
    
    # Configuration Manager
    config_manager_content = '''"""Configuration Management System"""
import json
import os
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

@dataclass
class TrajectoryConfig:
    """Trajectory configuration parameters"""
    trajectory_type: str = "CIRCULAR"
    duration: float = 300.0  # seconds
    sampling_rate: float = 100.0  # Hz
    # Trajectory-specific parameters
    radius: float = 100.0  # meters
    speed: float = 5.0  # m/s
    center_lat: float = 42.3601  # degrees
    center_lon: float = -71.0589  # degrees
    center_alt: float = 0.0  # meters
    # Figure-8 specific
    figure8_width: float = 200.0  # meters
    figure8_height: float = 100.0  # meters
    # Square specific
    square_size: float = 200.0  # meters
    # Spiral specific
    spiral_turns: int = 3
    spiral_pitch: float = 50.0  # meters per turn
    # Survey lines specific
    survey_spacing: float = 50.0  # meters
    survey_lines: int = 5
    # Coastal patrol specific
    coastal_waypoints: int = 8
    coastal_irregularity: float = 0.2  # randomness factor

@dataclass
class SensorConfig:
    """Sensor configuration parameters"""
    # IMU Quality template
    imu_quality: str = "NAVIGATION"  # CONSUMER, NAVIGATION, SURVEY
    
    # Individual sensor rates (Hz)
    accelerometer_rate: float = 100.0
    gyroscope_rate: float = 100.0
    magnetometer_rate: float = 20.0
    gps_position_rate: float = 10.0
    gps_velocity_rate: float = 10.0
    
    # Sensor participation (for selective fusion)
    use_accelerometer: bool = True
    use_gyroscope: bool = True
    use_magnetometer: bool = True
    use_gps_position: bool = True
    use_gps_velocity: bool = True
    
    # Noise parameters (will be set based on quality template)
    accelerometer_noise: float = 0.1  # m/s²
    gyroscope_noise: float = 0.01  # rad/s
    magnetometer_noise: float = 0.5  # µT
    gps_position_noise: float = 2.0  # meters
    gps_velocity_noise: float = 0.1  # m/s

@dataclass
class EnvironmentConfig:
    """Environmental conditions configuration"""
    condition_template: str = "GOOD"  # IDEAL, GOOD, MODERATE, POOR, EXTREME
    
    # GPS accuracy (meters)
    gps_horizontal_accuracy: float = 2.0
    gps_vertical_accuracy: float = 3.0
    gps_velocity_accuracy: float = 0.1
    
    # Environmental factors
    magnetic_declination: float = -14.0  # degrees
    magnetic_inclination: float = 70.0  # degrees
    magnetic_field_strength: float = 50.0  # µT
    
    # Disturbance factors
    gps_outage_probability: float = 0.0  # 0.0 to 1.0
    multipath_factor: float = 1.0  # multiplier for GPS noise

class ConfigurationManager:
    """Manages all configuration data and templates"""
    
    def __init__(self):
        self.config_dir = Path("config")
        self.templates_dir = Path("config/templates")
        self.current_config = {
            "trajectory": TrajectoryConfig(),
            "sensors": SensorConfig(),
            "environment": EnvironmentConfig()
        }
        
    def load_trajectory_template(self, template_name: str) -> TrajectoryConfig:
        """Load trajectory configuration template"""
        template_path = self.templates_dir / "trajectories" / f"{template_name.lower()}.json"
        try:
            with open(template_path, 'r') as f:
                data = json.load(f)
            config = TrajectoryConfig(**data)
            self.current_config["trajectory"] = config
            logger.info(f"Loaded trajectory template: {template_name}")
            return config
        except Exception as e:
            logger.error(f"Failed to load trajectory template {template_name}: {e}")
            return TrajectoryConfig()
    
    def load_sensor_template(self, template_name: str) -> SensorConfig:
        """Load sensor configuration template"""
        template_path = self.templates_dir / "sensors" / f"{template_name.lower()}.json"
        try:
            with open(template_path, 'r') as f:
                data = json.load(f)
            config = SensorConfig(**data)
            self.current_config["sensors"] = config
            logger.info(f"Loaded sensor template: {template_name}")
            return config
        except Exception as e:
            logger.error(f"Failed to load sensor template {template_name}: {e}")
            return SensorConfig()
    
    def load_environment_template(self, template_name: str) -> EnvironmentConfig:
        """Load environment configuration template"""
        template_path = self.templates_dir / "environments" / f"{template_name.lower()}.json"
        try:
            with open(template_path, 'r') as f:
                data = json.load(f)
            config = EnvironmentConfig(**data)
            self.current_config["environment"] = config
            logger.info(f"Loaded environment template: {template_name}")
            return config
        except Exception as e:
            logger.error(f"Failed to load environment template {template_name}: {e}")
            return EnvironmentConfig()
    
    def save_configuration(self, filename: str) -> bool:
        """Save current configuration to file"""
        try:
            config_data = {
                "trajectory": asdict(self.current_config["trajectory"]),
                "sensors": asdict(self.current_config["sensors"]),
                "environment": asdict(self.current_config["environment"])
            }
            
            filepath = self.config_dir / f"{filename}.json"
            with open(filepath, 'w') as f:
                json.dump(config_data, f, indent=4)
            
            logger.info(f"Configuration saved to: {filepath}")
            return True
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def load_configuration(self, filename: str) -> bool:
        """Load configuration from file"""
        try:
            filepath = self.config_dir / f"{filename}.json"
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            self.current_config["trajectory"] = TrajectoryConfig(**data["trajectory"])
            self.current_config["sensors"] = SensorConfig(**data["sensors"])
            self.current_config["environment"] = EnvironmentConfig(**data["environment"])
            
            logger.info(f"Configuration loaded from: {filepath}")
            return True
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return False
    
    def get_trajectory_config(self) -> TrajectoryConfig:
        """Get current trajectory configuration"""
        return self.current_config["trajectory"]
    
    def get_sensor_config(self) -> SensorConfig:
        """Get current sensor configuration"""
        return self.current_config["sensors"]
    
    def get_environment_config(self) -> EnvironmentConfig:
        """Get current environment configuration"""
        return self.current_config["environment"]
    
    def update_sensor_quality(self, quality: str):
        """Update sensor parameters based on quality template"""
        sensor_config = self.current_config["sensors"]
        sensor_config.imu_quality = quality
        
        # Set noise parameters based on quality
        if quality == "CONSUMER":
            sensor_config.accelerometer_noise = 1.0
            sensor_config.gyroscope_noise = 0.1
            sensor_config.magnetometer_noise = 2.0
        elif quality == "NAVIGATION":
            sensor_config.accelerometer_noise = 0.1
            sensor_config.gyroscope_noise = 0.01
            sensor_config.magnetometer_noise = 0.5
        elif quality == "SURVEY":
            sensor_config.accelerometer_noise = 0.01
            sensor_config.gyroscope_noise = 0.001
            sensor_config.magnetometer_noise = 0.1
    
    def update_environment_quality(self, condition: str):
        """Update environment parameters based on condition template"""
        env_config = self.current_config["environment"]
        env_config.condition_template = condition
        
        # Set parameters based on condition
        if condition == "IDEAL":
            env_config.gps_horizontal_accuracy = 0.5
            env_config.gps_vertical_accuracy = 1.0
            env_config.gps_outage_probability = 0.0
            env_config.multipath_factor = 1.0
        elif condition == "GOOD":
            env_config.gps_horizontal_accuracy = 2.0
            env_config.gps_vertical_accuracy = 3.0
            env_config.gps_outage_probability = 0.01
            env_config.multipath_factor = 1.2
        elif condition == "MODERATE":
            env_config.gps_horizontal_accuracy = 5.0
            env_config.gps_vertical_accuracy = 8.0
            env_config.gps_outage_probability = 0.05
            env_config.multipath_factor = 1.5
        elif condition == "POOR":
            env_config.gps_horizontal_accuracy = 10.0
            env_config.gps_vertical_accuracy = 15.0
            env_config.gps_outage_probability = 0.1
            env_config.multipath_factor = 2.0
        elif condition == "EXTREME":
            env_config.gps_horizontal_accuracy = 20.0
            env_config.gps_vertical_accuracy = 30.0
            env_config.gps_outage_probability = 0.2
            env_config.multipath_factor = 3.0
'''
    
    os.makedirs("config", exist_ok=True)
    with open("config/configuration_manager.py", "w") as f:
        f.write(config_manager_content)

def generate_gui_system():
    """Generate complete GUI system"""
    
    # Main GUI
    main_gui_content = '''"""Main GUI Window and Tab Management"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from gui.configuration_tab import ConfigurationTab
from gui.visualization_tab import VisualizationTab
from config.configuration_manager import ConfigurationManager
from simulation.simulation_engine import SimulationEngine
from fusion.fusion_engine import FusionEngine
from analysis.analysis_engine import AnalysisEngine

logger = logging.getLogger(__name__)

class MainGUI:
    """Main application GUI window"""
    
    def __init__(self, root):
        self.root = root
        self.setup_main_window()
        
        # Initialize core components
        self.config_manager = ConfigurationManager()
        self.simulation_engine = SimulationEngine()
        self.fusion_engine = FusionEngine()
        self.analysis_engine = AnalysisEngine()
        
        # Create GUI components
        self.create_menu_bar()
        self.create_main_notebook()
        self.create_status_bar()
        
        logger.info("Main GUI initialized successfully")
    
    def setup_main_window(self):
        """Setup main window properties"""
        self.root.title("INS/GPS Fusion Testing Platform")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Set application icon (if available)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
    
    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Configuration", command=self.new_configuration)
        file_menu.add_command(label="Open Configuration...", command=self.open_configuration)
        file_menu.add_command(label="Save Configuration...", command=self.save_configuration)
        file_menu.add_separator()
        file_menu.add_command(label="Export Data...", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Run Simulation", command=self.run_simulation)
        tools_menu.add_command(label="Clear Results", command=self.clear_results)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_main_notebook(self):
        """Create main tab notebook"""
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create tabs
        self.config_tab = ConfigurationTab(self.notebook, self.config_manager, self)
        self.viz_tab = VisualizationTab(self.notebook, self.analysis_engine, self)
        
        # Add tabs to notebook
        self.notebook.add(self.config_tab.frame, text="Configuration & Simulation")
        self.notebook.add(self.viz_tab.frame, text="Visualization & Results")
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_bar, variable=self.progress_var)
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2, fill=tk.X, expand=True)
    
    def update_status(self, message: str):
        """Update status bar message"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def update_progress(self, value: float):
        """Update progress bar (0-100)"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def new_configuration(self):
        """Create new configuration"""
        result = messagebox.askyesno("New Configuration", 
                                   "Create new configuration? Current settings will be lost.")
        if result:
            self.config_manager = ConfigurationManager()
            self.config_tab.refresh_ui()
            self.update_status("New configuration created")
    
    def open_configuration(self):
        """Open configuration file"""
        filename = filedialog.askopenfilename(
            title="Open Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            defaultextension=".json"
        )
        if filename:
            # Extract just the filename without extension
            config_name = os.path.splitext(os.path.basename(filename))[0]
            if self.config_manager.load_configuration(config_name):
                self.config_tab.refresh_ui()
                self.update_status(f"Configuration loaded: {config_name}")
            else:
                messagebox.showerror("Error", "Failed to load configuration")
    
    def save_configuration(self):
        """Save configuration file"""
        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            defaultextension=".json"
        )
        if filename:
            # Extract just the filename without extension
            config_name = os.path.splitext(os.path.basename(filename))[0]
            if self.config_manager.save_configuration(config_name):
                self.update_status(f"Configuration saved: {config_name}")
            else:
                messagebox.showerror("Error", "Failed to save configuration")
    
    def export_data(self):
        """Export simulation data"""
        if not hasattr(self, 'simulation_data') or self.simulation_data is None:
            messagebox.showwarning("No Data", "No simulation data to export. Run simulation first.")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Export Data",
            filetypes=[("CSV files", "*.csv"), ("MAT files", "*.mat"), ("HDF5 files", "*.h5")],
            defaultextension=".csv"
        )
        if filename:
            try:
                self.analysis_engine.export_data(self.simulation_data, filename)
                self.update_status(f"Data exported: {filename}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export data:\\n{str(e)}")
    
    def run_simulation(self):
        """Run complete simulation and analysis"""
        try:
            self.update_status("Starting simulation...")
            self.update_progress(0)
            
            # Get current configuration
            traj_config = self.config_manager.get_trajectory_config()
            sensor_config = self.config_manager.get_sensor_config()
            env_config = self.config_manager.get_environment_config()
            
            # Run simulation
            self.update_progress(20)
            self.simulation_data = self.simulation_engine.run_simulation(
                traj_config, sensor_config, env_config
            )
            
            # Run fusion
            self.update_progress(50)
            self.estimation_results = self.fusion_engine.run_fusion(
                self.simulation_data, sensor_config
            )
            
            # Run analysis
            self.update_progress(80)
            self.analysis_results = self.analysis_engine.run_analysis(
                self.simulation_data, self.estimation_results
            )
            
            # Update visualization tab
            self.update_progress(90)
            self.viz_tab.update_results(self.analysis_results)
            
            # Switch to visualization tab
            self.notebook.select(1)
            
            self.update_progress(100)
            self.update_status("Simulation completed successfully")
            
        except Exception as e:
            logger.error(f"Simulation failed: {e}")
            messagebox.showerror("Simulation Error", f"Simulation failed:\\n{str(e)}")
            self.update_status("Simulation failed")
            self.update_progress(0)
    
    def clear_results(self):
        """Clear all simulation results"""
        self.simulation_data = None
        self.estimation_results = None
        self.analysis_results = None
        self.viz_tab.clear_results()
        self.update_status("Results cleared")
        self.update_progress(0)
    
    def show_about(self):
        """Show about dialog"""
        about_text = """INS/GPS Fusion Testing Platform
        
Production-ready testing platform for boat motion compensation evaluation.

Features:
• 7 trajectory types with configurable parameters
• Complete sensor simulation with selective fusion
• Integration with 28-state Extended Kalman Filter
• Comprehensive 2D/3D visualization
• Statistical analysis and error computation
• Data export in multiple formats

Developed for marine navigation and motion compensation applications.
"""
        messagebox.showinfo("About", about_text)
'''
    
    os.makedirs("gui", exist_ok=True)
    with open("gui/main_gui.py", "w") as f:
        f.write(main_gui_content)
    
    # Configuration Tab
    config_tab_content = '''"""Configuration Tab - Trajectory, Sensor, and Environment Setup"""
import tkinter as tk
from tkinter import ttk, messagebox
import logging
from config.configuration_manager import ConfigurationManager

logger = logging.getLogger(__name__)

class ConfigurationTab:
    """Configuration tab for simulation setup"""
    
    def __init__(self, parent, config_manager: ConfigurationManager, main_gui):
        self.parent = parent
        self.config_manager = config_manager
        self.main_gui = main_gui
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Create UI components
        self.create_trajectory_section()
        self.create_sensor_section()
        self.create_environment_section()
        self.create_control_buttons()
        
        logger.info("Configuration tab initialized")
    
    def create_trajectory_section(self):
        """Create trajectory configuration section"""
        # Trajectory frame
        traj_frame = ttk.LabelFrame(self.frame, text="Trajectory Configuration")
        traj_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Trajectory type selection
        ttk.Label(traj_frame, text="Trajectory Type:").grid(row=0, column=0, sticky=tk.W)
        self.traj_type_var = tk.StringVar(value="CIRCULAR")
        traj_combo = ttk.Combobox(traj_frame, textvariable=self.traj_type_var,
                                 values=["CIRCULAR", "FIGURE8", "SQUARE", "STRAIGHT", 
                                        "SPIRAL", "SURVEY_LINES", "COASTAL_PATROL"])
        traj_combo.grid(row=0, column=1, sticky=tk.W)
        traj_combo.bind("<<ComboboxSelected>>", self.on_trajectory_type_changed)
        
        # Load template button
        ttk.Button(traj_frame, text="Load Template", 
                  command=self.load_trajectory_template).grid(row=0, column=2, padx=5)
        
        # Duration and sampling
        ttk.Label(traj_frame, text="Duration (s):").grid(row=1, column=0, sticky=tk.W)
        self.duration_var = tk.DoubleVar(value=300.0)
        duration_spinbox = tk.Spinbox(traj_frame, textvariable=self.duration_var,
                                     from_=10, to=3600, increment=10, width=10)
        duration_spinbox.grid(row=1, column=1, sticky=tk.W)
        
        ttk.Label(traj_frame, text="Sampling Rate (Hz):").grid(row=2, column=0, sticky=tk.W)
        self.sampling_rate_var = tk.DoubleVar(value=100.0)
        sampling_spinbox = tk.Spinbox(traj_frame, textvariable=self.sampling_rate_var,
                                     from_=1, to=1000, increment=10, width=10)
        sampling_spinbox.grid(row=2, column=1, sticky=tk.W)
        
        # Trajectory-specific parameters frame
        self.traj_params_frame = ttk.Frame(traj_frame)
        self.traj_params_frame.grid(row=3, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        # Center location
        ttk.Label(traj_frame, text="Center Lat (deg):").grid(row=4, column=0, sticky=tk.W)
        self.center_lat_var = tk.DoubleVar(value=42.3601)
        lat_spinbox = tk.Spinbox(traj_frame, textvariable=self.center_lat_var,
                                from_=-90, to=90, increment=0.1, width=10, format="%.4f")
        lat_spinbox.grid(row=4, column=1, sticky=tk.W)
        
        ttk.Label(traj_frame, text="Center Lon (deg):").grid(row=5, column=0, sticky=tk.W)
        self.center_lon_var = tk.DoubleVar(value=-71.0589)
        lon_spinbox = tk.Spinbox(traj_frame, textvariable=self.center_lon_var,
                                from_=-180, to=180, increment=0.1, width=10, format="%.4f")
        lon_spinbox.grid(row=5, column=1, sticky=tk.W)
        
        # Create initial trajectory parameters
        self.create_trajectory_parameters()
    
    def create_trajectory_parameters(self):
        """Create trajectory-specific parameter widgets"""
        # Clear existing parameters
        for widget in self.traj_params_frame.winfo_children():
            widget.destroy()
        
        traj_type = self.traj_type_var.get()
        
        if traj_type == "CIRCULAR":
            ttk.Label(self.traj_params_frame, text="Radius (m):").grid(row=0, column=0, sticky=tk.W)
            self.radius_var = tk.DoubleVar(value=100.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.radius_var,
                      from_=10, to=1000, increment=10, width=10).grid(row=0, column=1)
            
            ttk.Label(self.traj_params_frame, text="Speed (m/s):").grid(row=0, column=2, sticky=tk.W)
            self.speed_var = tk.DoubleVar(value=5.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.speed_var,
                      from_=0.1, to=20, increment=0.5, width=10).grid(row=0, column=3)
        
        elif traj_type == "FIGURE8":
            ttk.Label(self.traj_params_frame, text="Width (m):").grid(row=0, column=0, sticky=tk.W)
            self.fig8_width_var = tk.DoubleVar(value=200.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.fig8_width_var,
                      from_=50, to=1000, increment=25, width=10).grid(row=0, column=1)
            
            ttk.Label(self.traj_params_frame, text="Height (m):").grid(row=0, column=2, sticky=tk.W)
            self.fig8_height_var = tk.DoubleVar(value=100.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.fig8_height_var,
                      from_=25, to=500, increment=25, width=10).grid(row=0, column=3)
        
        elif traj_type == "SQUARE":
            ttk.Label(self.traj_params_frame, text="Size (m):").grid(row=0, column=0, sticky=tk.W)
            self.square_size_var = tk.DoubleVar(value=200.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.square_size_var,
                      from_=50, to=1000, increment=25, width=10).grid(row=0, column=1)
        
        elif traj_type == "SPIRAL":
            ttk.Label(self.traj_params_frame, text="Turns:").grid(row=0, column=0, sticky=tk.W)
            self.spiral_turns_var = tk.IntVar(value=3)
            tk.Spinbox(self.traj_params_frame, textvariable=self.spiral_turns_var,
                      from_=1, to=10, increment=1, width=10).grid(row=0, column=1)
            
            ttk.Label(self.traj_params_frame, text="Pitch (m):").grid(row=0, column=2, sticky=tk.W)
            self.spiral_pitch_var = tk.DoubleVar(value=50.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.spiral_pitch_var,
                      from_=10, to=200, increment=10, width=10).grid(row=0, column=3)
        
        elif traj_type == "SURVEY_LINES":
            ttk.Label(self.traj_params_frame, text="Spacing (m):").grid(row=0, column=0, sticky=tk.W)
            self.survey_spacing_var = tk.DoubleVar(value=50.0)
            tk.Spinbox(self.traj_params_frame, textvariable=self.survey_spacing_var,
                      from_=10, to=200, increment=10, width=10).grid(row=0, column=1)
            
            ttk.Label(self.traj_params_frame, text="Lines:").grid(row=0, column=2, sticky=tk.W)
            self.survey_lines_var = tk.IntVar(value=5)
            tk.Spinbox(self.traj_params_frame, textvariable=self.survey_lines_var,
                      from_=2, to=20, increment=1, width=10).grid(row=0, column=3)
    
    def create_sensor_section(self):
        """Create sensor configuration section"""
        # Main sensor frame
        sensor_frame = ttk.LabelFrame(self.frame, text="Sensor Configuration")
        sensor_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Quality template
        ttk.Label(sensor_frame, text="IMU Quality:").grid(row=0, column=0, sticky=tk.W)
        self.imu_quality_var = tk.StringVar(value="NAVIGATION")
        quality_combo = ttk.Combobox(sensor_frame, textvariable=self.imu_quality_var,
                                    values=["CONSUMER", "NAVIGATION", "SURVEY"])
        quality_combo.grid(row=0, column=1, sticky=tk.W)
        quality_combo.bind("<<ComboboxSelected>>", self.on_sensor_quality_changed)
        
        ttk.Button(sensor_frame, text="Load Template", 
                  command=self.load_sensor_template).grid(row=0, column=2, padx=5)
        
        # Individual sensor rates
        rates_frame = ttk.LabelFrame(sensor_frame, text="Sensor Rates (Hz)")
        rates_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        ttk.Label(rates_frame, text="Accelerometer:").grid(row=0, column=0, sticky=tk.W)
        self.accel_rate_var = tk.DoubleVar(value=100.0)
        tk.Spinbox(rates_frame, textvariable=self.accel_rate_var,
                  from_=1, to=1000, increment=10, width=8).grid(row=0, column=1)
        
        ttk.Label(rates_frame, text="Gyroscope:").grid(row=0, column=2, sticky=tk.W)
        self.gyro_rate_var = tk.DoubleVar(value=100.0)
        tk.Spinbox(rates_frame, textvariable=self.gyro_rate_var,
                  from_=1, to=1000, increment=10, width=8).grid(row=0, column=3)
        
        ttk.Label(rates_frame, text="Magnetometer:").grid(row=1, column=0, sticky=tk.W)
        self.mag_rate_var = tk.DoubleVar(value=20.0)
        tk.Spinbox(rates_frame, textvariable=self.mag_rate_var,
                  from_=1, to=100, increment=5, width=8).grid(row=1, column=1)
        
        ttk.Label(rates_frame, text="GPS Position:").grid(row=1, column=2, sticky=tk.W)
        self.gps_pos_rate_var = tk.DoubleVar(value=10.0)
        tk.Spinbox(rates_frame, textvariable=self.gps_pos_rate_var,
                  from_=0.1, to=100, increment=1, width=8).grid(row=1, column=3)
        
        # Sensor participation
        participation_frame = ttk.LabelFrame(sensor_frame, text="Sensor Participation")
        participation_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        self.use_accel_var = tk.BooleanVar(value=True)
        self.use_gyro_var = tk.BooleanVar(value=True)
        self.use_mag_var = tk.BooleanVar(value=True)
        self.use_gps_pos_var = tk.BooleanVar(value=True)
        self.use_gps_vel_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(participation_frame, text="Accelerometer", 
                       variable=self.use_accel_var).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(participation_frame, text="Gyroscope", 
                       variable=self.use_gyro_var).grid(row=0, column=1, sticky=tk.W)
        ttk.Checkbutton(participation_frame, text="Magnetometer", 
                       variable=self.use_mag_var).grid(row=0, column=2, sticky=tk.W)
        ttk.Checkbutton(participation_frame, text="GPS Position", 
                       variable=self.use_gps_pos_var).grid(row=1, column=0, sticky=tk.W)
        ttk.Checkbutton(participation_frame, text="GPS Velocity", 
                       variable=self.use_gps_vel_var).grid(row=1, column=1, sticky=tk.W)
    
    def create_environment_section(self):
        """Create environment configuration section"""
        env_frame = ttk.LabelFrame(self.frame, text="Environmental Conditions")
        env_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Condition template
        ttk.Label(env_frame, text="Condition:").grid(row=0, column=0, sticky=tk.W)
        self.env_condition_var = tk.StringVar(value="GOOD")
        condition_combo = ttk.Combobox(env_frame, textvariable=self.env_condition_var,
                                      values=["IDEAL", "GOOD", "MODERATE", "POOR", "EXTREME"])
        condition_combo.grid(row=0, column=1, sticky=tk.W)
        condition_combo.bind("<<ComboboxSelected>>", self.on_environment_changed)
        
        ttk.Button(env_frame, text="Load Template", 
                  command=self.load_environment_template).grid(row=0, column=2, padx=5)
        
        # GPS accuracy controls
        gps_frame = ttk.LabelFrame(env_frame, text="GPS Accuracy (meters)")
        gps_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        ttk.Label(gps_frame, text="Horizontal:").grid(row=0, column=0, sticky=tk.W)
        self.gps_horiz_var = tk.DoubleVar(value=2.0)
        tk.Spinbox(gps_frame, textvariable=self.gps_horiz_var,
                  from_=0.1, to=50, increment=0.5, width=8).grid(row=0, column=1)
        
        ttk.Label(gps_frame, text="Vertical:").grid(row=0, column=2, sticky=tk.W)
        self.gps_vert_var = tk.DoubleVar(value=3.0)
        tk.Spinbox(gps_frame, textvariable=self.gps_vert_var,
                  from_=0.1, to=100, increment=1, width=8).grid(row=0, column=3)
        
        ttk.Label(gps_frame, text="Velocity:").grid(row=1, column=0, sticky=tk.W)
        self.gps_vel_var = tk.DoubleVar(value=0.1)
        tk.Spinbox(gps_frame, textvariable=self.gps_vel_var,
                  from_=0.01, to=5, increment=0.05, width=8).grid(row=1, column=1)
    
    def create_control_buttons(self):
        """Create control buttons"""
        button_frame = ttk.Frame(self.frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(button_frame, text="Run Simulation", 
                  command=self.main_gui.run_simulation).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Configuration", 
                  command=self.save_current_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Reset to Defaults", 
                  command=self.reset_to_defaults).pack(side=tk.LEFT, padx=5)
    
    def on_trajectory_type_changed(self, event=None):
        """Handle trajectory type change"""
        self.create_trajectory_parameters()
        self.update_configuration()
    
    def on_sensor_quality_changed(self, event=None):
        """Handle sensor quality change"""
        self.config_manager.update_sensor_quality(self.imu_quality_var.get())
    
    def on_environment_changed(self, event=None):
        """Handle environment condition change"""
        self.config_manager.update_environment_quality(self.env_condition_var.get())
        self.refresh_environment_ui()
    
    def load_trajectory_template(self):
        """Load trajectory template"""
        template_name = self.traj_type_var.get()
        self.config_manager.load_trajectory_template(template_name)
        self.refresh_trajectory_ui()
    
    def load_sensor_template(self):
        """Load sensor template"""
        template_name = self.imu_quality_var.get()
        self.config_manager.load_sensor_template(template_name)
        self.refresh_sensor_ui()
    
    def load_environment_template(self):
        """Load environment template"""
        template_name = self.env_condition_var.get()
        self.config_manager.load_environment_template(template_name)
        self.refresh_environment_ui()
    
    def refresh_trajectory_ui(self):
        """Refresh trajectory UI with current configuration"""
        config = self.config_manager.get_trajectory_config()
        self.duration_var.set(config.duration)
        self.sampling_rate_var.set(config.sampling_rate)
        self.center_lat_var.set(config.center_lat)
        self.center_lon_var.set(config.center_lon)
        
        # Set trajectory-specific parameters
        if hasattr(self, 'radius_var'):
            self.radius_var.set(config.radius)
        if hasattr(self, 'speed_var'):
            self.speed_var.set(config.speed)
    
    def refresh_sensor_ui(self):
        """Refresh sensor UI with current configuration"""
        config = self.config_manager.get_sensor_config()
        self.accel_rate_var.set(config.accelerometer_rate)
        self.gyro_rate_var.set(config.gyroscope_rate)
        self.mag_rate_var.set(config.magnetometer_rate)
        self.gps_pos_rate_var.set(config.gps_position_rate)
        
        self.use_accel_var.set(config.use_accelerometer)
        self.use_gyro_var.set(config.use_gyroscope)
        self.use_mag_var.set(config.use_magnetometer)
        self.use_gps_pos_var.set(config.use_gps_position)
        self.use_gps_vel_var.set(config.use_gps_velocity)
    
    def refresh_environment_ui(self):
        """Refresh environment UI with current configuration"""
        config = self.config_manager.get_environment_config()
        self.gps_horiz_var.set(config.gps_horizontal_accuracy)
        self.gps_vert_var.set(config.gps_vertical_accuracy)
        self.gps_vel_var.set(config.gps_velocity_accuracy)
    
    def refresh_ui(self):
        """Refresh entire UI with current configuration"""
        self.refresh_trajectory_ui()
        self.refresh_sensor_ui()
        self.refresh_environment_ui()
    
    def update_configuration(self):
        """Update configuration manager with current UI values"""
        # Update trajectory config
        traj_config = self.config_manager.get_trajectory_config()
        traj_config.trajectory_type = self.traj_type_var.get()
        traj_config.duration = self.duration_var.get()
        traj_config.sampling_rate = self.sampling_rate_var.get()
        traj_config.center_lat = self.center_lat_var.get()
        traj_config.center_lon = self.center_lon_var.get()
        
        # Update sensor config  
        sensor_config = self.config_manager.get_sensor_config()
        sensor_config.accelerometer_rate = self.accel_rate_var.get()
        sensor_config.gyroscope_rate = self.gyro_rate_var.get()
        sensor_config.magnetometer_rate = self.mag_rate_var.get()
        sensor_config.gps_position_rate = self.gps_pos_rate_var.get()
        
        sensor_config.use_accelerometer = self.use_accel_var.get()
        sensor_config.use_gyroscope = self.use_gyro_var.get()
        sensor_config.use_magnetometer = self.use_mag_var.get()
        sensor_config.use_gps_position = self.use_gps_pos_var.get()
        sensor_config.use_gps_velocity = self.use_gps_vel_var.get()
        
        # Update environment config
        env_config = self.config_manager.get_environment_config()
        env_config.gps_horizontal_accuracy = self.gps_horiz_var.get()
        env_config.gps_vertical_accuracy = self.gps_vert_var.get()
        env_config.gps_velocity_accuracy = self.gps_vel_var.get()
    
    def save_current_config(self):
        """Save current configuration"""
        self.update_configuration()
        self.main_gui.save_configuration()
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        result = messagebox.askyesno("Reset Configuration", 
                                   "Reset all settings to defaults?")
        if result:
            self.config_manager = ConfigurationManager()
            self.refresh_ui()
            self.main_gui.update_status("Configuration reset to defaults")
'''
    
    with open("gui/configuration_tab.py", "w") as f:
        f.write(config_tab_content)
    
    # Visualization Tab
    viz_tab_content = '''"""Visualization Tab - Results Display and Analysis"""
import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import matplotlib.patches as patches
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import logging

logger = logging.getLogger(__name__)

class VisualizationTab:
    """Visualization tab for results display"""
    
    def __init__(self, parent, analysis_engine, main_gui):
        self.parent = parent
        self.analysis_engine = analysis_engine
        self.main_gui = main_gui
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Initialize plot variables
        self.analysis_results = None
        
        # Create UI
        self.create_plot_controls()
        self.create_plot_notebook()
        
        logger.info("Visualization tab initialized")
    
    def create_plot_controls(self):
        """Create plot control buttons"""
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(control_frame, text="Refresh Plots", 
                  command=self.refresh_plots).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Export Plots", 
                  command=self.export_plots).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Clear All", 
                  command=self.clear_results).pack(side=tk.LEFT, padx=5)
        
        # Statistics label
        self.stats_label = ttk.Label(control_frame, text="No results available")
        self.stats_label.pack(side=tk.RIGHT, padx=5)
    
    def create_plot_notebook(self):
        """Create notebook for different plot categories"""
        self.plot_notebook = ttk.Notebook(self.frame)
        self.plot_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create plot tabs
        self.create_trajectory_plots_tab()
        self.create_error_analysis_tab()
        self.create_3d_visualization_tab()
        self.create_statistics_tab()
    
    def create_trajectory_plots_tab(self):
        """Create trajectory plots tab"""
        traj_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(traj_frame, text="Trajectory Plots")
        
        # Create matplotlib figure for 2D trajectory
        self.traj_fig, self.traj_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.traj_fig.suptitle("Trajectory Analysis")
        
        # 2D trajectory plot
        self.traj_axes[0, 0].set_title("2D Trajectory Comparison")
        self.traj_axes[0, 0].set_xlabel("East (m)")
        self.traj_axes[0, 0].set_ylabel("North (m)")
        self.traj_axes[0, 0].grid(True)
        self.traj_axes[0, 0].axis('equal')
        
        # Position vs time
        self.traj_axes[0, 1].set_title("Position vs Time")
        self.traj_axes[0, 1].set_xlabel("Time (s)")
        self.traj_axes[0, 1].set_ylabel("Position (m)")
        self.traj_axes[0, 1].grid(True)
        
        # Speed vs time
        self.traj_axes[1, 0].set_title("Speed vs Time")
        self.traj_axes[1, 0].set_xlabel("Time (s)")
        self.traj_axes[1, 0].set_ylabel("Speed (m/s)")
        self.traj_axes[1, 0].grid(True)
        
        # Altitude vs time
        self.traj_axes[1, 1].set_title("Altitude vs Time")
        self.traj_axes[1, 1].set_xlabel("Time (s)")
        self.traj_axes[1, 1].set_ylabel("Altitude (m)")
        self.traj_axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        # Embed in tkinter
        self.traj_canvas = FigureCanvasTkAgg(self.traj_fig, traj_frame)
        self.traj_canvas.draw()
        self.traj_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add toolbar
        toolbar_frame = ttk.Frame(traj_frame)
        toolbar_frame.pack(fill=tk.X)
        self.traj_toolbar = NavigationToolbar2Tk(self.traj_canvas, toolbar_frame)
        self.traj_toolbar.update()
    
    def create_error_analysis_tab(self):
        """Create error analysis tab"""
        error_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(error_frame, text="Error Analysis")
        
        # Create matplotlib figure
        self.error_fig, self.error_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.error_fig.suptitle("Error Analysis")
        
        # Position error vs time
        self.error_axes[0, 0].set_title("Position Error vs Time")
        self.error_axes[0, 0].set_xlabel("Time (s)")
        self.error_axes[0, 0].set_ylabel("Position Error (m)")
        self.error_axes[0, 0].grid(True)
        
        # Error distribution
        self.error_axes[0, 1].set_title("Position Error Distribution")
        self.error_axes[0, 1].set_xlabel("Position Error (m)")
        self.error_axes[0, 1].set_ylabel("Frequency")
        self.error_axes[0, 1].grid(True)
        
        # Velocity error vs time
        self.error_axes[1, 0].set_title("Velocity Error vs Time")
        self.error_axes[1, 0].set_xlabel("Time (s)")
        self.error_axes[1, 0].set_ylabel("Velocity Error (m/s)")
        self.error_axes[1, 0].grid(True)
        
        # Orientation error vs time
        self.error_axes[1, 1].set_title("Orientation Error vs Time")
        self.error_axes[1, 1].set_xlabel("Time (s)")
        self.error_axes[1, 1].set_ylabel("Orientation Error (deg)")
        self.error_axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        # Embed in tkinter
        self.error_canvas = FigureCanvasTkAgg(self.error_fig, error_frame)
        self.error_canvas.draw()
        self.error_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add toolbar
        toolbar_frame = ttk.Frame(error_frame)
        toolbar_frame.pack(fill=tk.X)
        self.error_toolbar = NavigationToolbar2Tk(self.error_canvas, toolbar_frame)
        self.error_toolbar.update()
    
    def create_3d_visualization_tab(self):
        """Create 3D visualization tab"""
        viz3d_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(viz3d_frame, text="3D Visualization")
        
        # Create matplotlib 3D figure
        self.viz3d_fig = plt.figure(figsize=(14, 8))
        
        # 3D trajectory plot
        self.ax3d_traj = self.viz3d_fig.add_subplot(121, projection='3d')
        self.ax3d_traj.set_title("3D Trajectory")
        self.ax3d_traj.set_xlabel("East (m)")
        self.ax3d_traj.set_ylabel("North (m)")
        self.ax3d_traj.set_zlabel("Up (m)")
        
        # 3D orientation plot
        self.ax3d_orient = self.viz3d_fig.add_subplot(122, projection='3d')
        self.ax3d_orient.set_title("Vessel Orientation")
        self.ax3d_orient.set_xlabel("X")
        self.ax3d_orient.set_ylabel("Y")
        self.ax3d_orient.set_zlabel("Z")
        
        plt.tight_layout()
        
        # Embed in tkinter
        self.viz3d_canvas = FigureCanvasTkAgg(self.viz3d_fig, viz3d_frame)
        self.viz3d_canvas.draw()
        self.viz3d_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Add toolbar
        toolbar_frame = ttk.Frame(viz3d_frame)
        toolbar_frame.pack(fill=tk.X)
        self.viz3d_toolbar = NavigationToolbar2Tk(self.viz3d_canvas, toolbar_frame)
        self.viz3d_toolbar.update()
    
    def create_statistics_tab(self):
        """Create statistics display tab"""
        stats_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(stats_frame, text="Statistics")
        
        # Create text widget for statistics
        text_frame = ttk.Frame(stats_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Statistics text with scrollbar
        self.stats_text = tk.Text(text_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def update_results(self, analysis_results):
        """Update visualization with new results"""
        self.analysis_results = analysis_results
        
        if analysis_results is None:
            return
        
        try:
            self.update_trajectory_plots()
            self.update_error_plots()
            self.update_3d_plots()
            self.update_statistics()
            self.update_stats_summary()
            
            logger.info("Visualization updated successfully")
        except Exception as e:
            logger.error(f"Failed to update visualization: {e}")
    
    def update_trajectory_plots(self):
        """Update trajectory plots"""
        if self.analysis_results is None:
            return
        
        # Clear previous plots
        for ax in self.traj_axes.flat:
            ax.clear()
        
        # Get data
        ground_truth = self.analysis_results['ground_truth']
        estimates = self.analysis_results['estimates']
        gps_measurements = self.analysis_results.get('gps_measurements', None)
        
        time_vec = ground_truth['time']
        true_pos = ground_truth['position']  # [N, 3] array
        est_pos = estimates['position']      # [N, 3] array
        
        # 2D trajectory plot
        ax = self.traj_axes[0, 0]
        ax.plot(true_pos[:, 1], true_pos[:, 0], 'b-', label='True Trajectory', linewidth=2)
        ax.plot(est_pos[:, 1], est_pos[:, 0], 'r--', label='Estimated', linewidth=2)
        
        if gps_measurements is not None:
            gps_pos = gps_measurements['position']
            ax.scatter(gps_pos[:, 1], gps_pos[:, 0], c='g', s=20, alpha=0.6, label='GPS Points')
        
        # Mark start and end
        ax.plot(true_pos[0, 1], true_pos[0, 0], 'go', markersize=8, label='Start')
        ax.plot(true_pos[-1, 1], true_pos[-1, 0], 'ro', markersize=8, label='End')
        
        ax.set_title("2D Trajectory Comparison")
        ax.set_xlabel("East (m)")
        ax.set_ylabel("North (m)")
        ax.grid(True)
        ax.legend()
        ax.axis('equal')
        
        # Position vs time
        ax = self.traj_axes[0, 1]
        ax.plot(time_vec, true_pos[:, 0], 'b-', label='True North', linewidth=2)
        ax.plot(time_vec, est_pos[:, 0], 'r--', label='Est North', linewidth=2)
        ax.plot(time_vec, true_pos[:, 1], 'g-', label='True East', linewidth=2)
        ax.plot(time_vec, est_pos[:, 1], 'm--', label='Est East', linewidth=2)
        ax.set_title("Position vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Position (m)")
        ax.grid(True)
        ax.legend()
        
        # Speed vs time
        ax = self.traj_axes[1, 0]
        true_vel = ground_truth['velocity']
        est_vel = estimates['velocity']
        true_speed = np.linalg.norm(true_vel, axis=1)
        est_speed = np.linalg.norm(est_vel, axis=1)
        
        ax.plot(time_vec, true_speed, 'b-', label='True Speed', linewidth=2)
        ax.plot(time_vec, est_speed, 'r--', label='Estimated Speed', linewidth=2)
        ax.set_title("Speed vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Speed (m/s)")
        ax.grid(True)
        ax.legend()
        
        # Altitude vs time
        ax = self.traj_axes[1, 1]
        ax.plot(time_vec, -true_pos[:, 2], 'b-', label='True Altitude', linewidth=2)
        ax.plot(time_vec, -est_pos[:, 2], 'r--', label='Estimated Altitude', linewidth=2)
        ax.set_title("Altitude vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Altitude (m)")
        ax.grid(True)
        ax.legend()
        
        self.traj_fig.suptitle("Trajectory Analysis")
        plt.tight_layout()
        self.traj_canvas.draw()
    
    def update_error_plots(self):
        """Update error analysis plots"""
        if self.analysis_results is None:
            return
        
        # Clear previous plots
        for ax in self.error_axes.flat:
            ax.clear()
        
        # Get error data
        errors = self.analysis_results['errors']
        time_vec = self.analysis_results['ground_truth']['time']
        
        # Position error vs time
        ax = self.error_axes[0, 0]
        pos_error_2d = np.linalg.norm(errors['position'][:, :2], axis=1)
        ax.plot(time_vec, pos_error_2d, 'r-', linewidth=2)
        ax.set_title("Position Error vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("2D Position Error (m)")
        ax.grid(True)
        
        # Error distribution
        ax = self.error_axes[0, 1]
        ax.hist(pos_error_2d, bins=50, alpha=0.7, density=True)
        ax.set_title("Position Error Distribution")
        ax.set_xlabel("Position Error (m)")
        ax.set_ylabel("Density")
        ax.grid(True)
        
        # Velocity error vs time
        ax = self.error_axes[1, 0]
        vel_error = np.linalg.norm(errors['velocity'], axis=1)
        ax.plot(time_vec, vel_error, 'g-', linewidth=2)
        ax.set_title("Velocity Error vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Velocity Error (m/s)")
        ax.grid(True)
        
        # Orientation error vs time
        ax = self.error_axes[1, 1]
        if 'orientation' in errors:
            orient_error = np.degrees(errors['orientation'])
            ax.plot(time_vec, orient_error, 'm-', linewidth=2)
        ax.set_title("Orientation Error vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Orientation Error (deg)")
        ax.grid(True)
        
        self.error_fig.suptitle("Error Analysis")
        plt.tight_layout()
        self.error_canvas.draw()
    
    def update_3d_plots(self):
        """Update 3D visualization plots"""
        if self.analysis_results is None:
            return
        
        # Clear previous plots
        self.ax3d_traj.clear()
        self.ax3d_orient.clear()
        
        # Get data
        ground_truth = self.analysis_results['ground_truth']
        estimates = self.analysis_results['estimates']
        
        true_pos = ground_truth['position']
        est_pos = estimates['position']
        true_orient = ground_truth.get('orientation', None)
        est_orient = estimates.get('orientation', None)
        
        # 3D trajectory plot
        self.ax3d_traj.plot(true_pos[:, 1], true_pos[:, 0], -true_pos[:, 2], 
                           'b-', label='True Trajectory', linewidth=2)
        self.ax3d_traj.plot(est_pos[:, 1], est_pos[:, 0], -est_pos[:, 2], 
                           'r--', label='Estimated', linewidth=2)
        
        # Mark start and end points
        self.ax3d_traj.scatter([true_pos[0, 1]], [true_pos[0, 0]], [-true_pos[0, 2]], 
                              c='g', s=100, label='Start')
        self.ax3d_traj.scatter([true_pos[-1, 1]], [true_pos[-1, 0]], [-true_pos[-1, 2]], 
                              c='r', s=100, label='End')
        
        self.ax3d_traj.set_title("3D Trajectory")
        self.ax3d_traj.set_xlabel("East (m)")
        self.ax3d_traj.set_ylabel("North (m)")
        self.ax3d_traj.set_zlabel("Up (m)")
        self.ax3d_traj.legend()
        
        # 3D orientation visualization (simplified)
        if true_orient is not None and est_orient is not None:
            # Sample orientations for visualization (every 50th point)
            sample_idx = np.arange(0, len(true_orient), max(len(true_orient)//20, 1))
            
            for i, idx in enumerate(sample_idx[:10]):  # Limit to 10 orientations
                pos = true_pos[idx]
                # Create coordinate axes (simplified representation)
                scale = 20.0
                
                # True orientation (blue)
                self.ax3d_orient.quiver(pos[1], pos[0], -pos[2], 
                                       scale, 0, 0, color='b', alpha=0.7)
                self.ax3d_orient.quiver(pos[1], pos[0], -pos[2], 
                                       0, scale, 0, color='b', alpha=0.7)
                
                # Estimated orientation (red) - slightly offset for visibility
                pos_est = est_pos[idx] + np.array([2, 2, 0])
                self.ax3d_orient.quiver(pos_est[1], pos_est[0], -pos_est[2], 
                                       scale*0.8, 0, 0, color='r', alpha=0.7)
                self.ax3d_orient.quiver(pos_est[1], pos_est[0], -pos_est[2], 
                                       0, scale*0.8, 0, color='r', alpha=0.7)
        
        self.ax3d_orient.set_title("Vessel Orientation (Simplified)")
        self.ax3d_orient.set_xlabel("East (m)")
        self.ax3d_orient.set_ylabel("North (m)")
        self.ax3d_orient.set_zlabel("Up (m)")
        
        self.viz3d_canvas.draw()
    
    def update_statistics(self):
        """Update statistics display"""
        if self.analysis_results is None:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, "No simulation results available.")
            return
        
        # Clear existing text
        self.stats_text.delete(1.0, tk.END)
        
        # Calculate statistics
        stats = self.analysis_results['statistics']
        
        stats_text = """INS/GPS Fusion Analysis Results
========================================

POSITION ERRORS:
----------------------------------------
RMS 2D Position Error:     {:.3f} m
Max 2D Position Error:     {:.3f} m
Mean 2D Position Error:    {:.3f} m
95% Position Error:        {:.3f} m

VELOCITY ERRORS:
----------------------------------------
RMS Velocity Error:        {:.3f} m/s
Max Velocity Error:        {:.3f} m/s
Mean Velocity Error:       {:.3f} m/s

ALTITUDE ERRORS:
----------------------------------------
RMS Altitude Error:        {:.3f} m
Max Altitude Error:        {:.3f} m
Mean Altitude Error:       {:.3f} m

TRAJECTORY STATISTICS:
----------------------------------------
Total Distance:            {:.1f} m
Average Speed:             {:.2f} m/s
Maximum Speed:             {:.2f} m/s
Simulation Duration:       {:.1f} s

SENSOR PARTICIPATION:
----------------------------------------
Accelerometer:             {}
Gyroscope:                 {}
Magnetometer:              {}
GPS Position:              {}
GPS Velocity:              {}

""".format(
            stats.get('rms_position_error_2d', 0),
            stats.get('max_position_error_2d', 0),
            stats.get('mean_position_error_2d', 0),
            stats.get('position_error_95th', 0),
            stats.get('rms_velocity_error', 0),
            stats.get('max_velocity_error', 0),
            stats.get('mean_velocity_error', 0),
            stats.get('rms_altitude_error', 0),
            stats.get('max_altitude_error', 0),
            stats.get('mean_altitude_error', 0),
            stats.get('total_distance', 0),
            stats.get('average_speed', 0),
            stats.get('maximum_speed', 0),
            stats.get('duration', 0),
            "Enabled" if stats.get('used_accelerometer', False) else "Disabled",
            "Enabled" if stats.get('used_gyroscope', False) else "Disabled",
            "Enabled" if stats.get('used_magnetometer', False) else "Disabled",
            "Enabled" if stats.get('used_gps_position', False) else "Disabled",
            "Enabled" if stats.get('used_gps_velocity', False) else "Disabled"
        )
        
        self.stats_text.insert(tk.END, stats_text)
    
    def update_stats_summary(self):
        """Update summary statistics in control bar"""
        if self.analysis_results is None:
            self.stats_label.config(text="No results available")
            return
        
        stats = self.analysis_results['statistics']
        rms_pos = stats.get('rms_position_error_2d', 0)
        max_pos = stats.get('max_position_error_2d', 0)
        
        summary = f"RMS Error: {rms_pos:.2f}m | Max Error: {max_pos:.2f}m"
        self.stats_label.config(text=summary)
    
    def refresh_plots(self):
        """Refresh all plots"""
        if self.analysis_results is not None:
            self.update_results(self.analysis_results)
    
    def export_plots(self):
        """Export all plots"""
        if self.analysis_results is None:
            tk.messagebox.showwarning("No Data", "No plots to export.")
            return
        
        try:
            # Export trajectory plots
            self.traj_fig.savefig("exports/trajectory_plots.png", dpi=300, bbox_inches='tight')
            
            # Export error plots
            self.error_fig.savefig("exports/error_analysis.png", dpi=300, bbox_inches='tight')
            
            # Export 3D plots
            self.viz3d_fig.savefig("exports/3d_visualization.png", dpi=300, bbox_inches='tight')
            
            tk.messagebox.showinfo("Export Complete", "Plots exported to exports/ directory")
        except Exception as e:
            tk.messagebox.showerror("Export Error", f"Failed to export plots:\\n{str(e)}")
    
    def clear_results(self):
        """Clear all results and plots"""
        self.analysis_results = None
        
        # Clear all axes
        for ax in self.traj_axes.flat:
            ax.clear()
            ax.grid(True)
        
        for ax in self.error_axes.flat:
            ax.clear()
            ax.grid(True)
        
        self.ax3d_traj.clear()
        self.ax3d_orient.clear()
        
        # Redraw canvases
        self.traj_canvas.draw()
        self.error_canvas.draw()
        self.viz3d_canvas.draw()
        
        # Clear statistics
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, "No simulation results available.")
        
        self.stats_label.config(text="No results available")
'''
    
    with open("gui/visualization_tab.py", "w") as f:
        f.write(viz_tab_content)

def generate_simulation_engine():
    """Generate simulation engine"""
    
    # Simulation Engine
    sim_engine_content = '''"""Simulation Engine - Trajectory and Sensor Simulation"""
import numpy as np
from scipy.spatial.transform import Rotation
import logging
from dataclasses import dataclass
from typing import Dict, Any, Tuple
from config.configuration_manager import TrajectoryConfig, SensorConfig, EnvironmentConfig

logger = logging.getLogger(__name__)

@dataclass
class GroundTruth:
    """Ground truth trajectory data"""
    time: np.ndarray
    position: np.ndarray  # [N, 3] NED coordinates
    velocity: np.ndarray  # [N, 3] NED velocities
    acceleration: np.ndarray  # [N, 3] NED accelerations
    orientation: np.ndarray  # [N, 4] quaternions [w, x, y, z]
    angular_velocity: np.ndarray  # [N, 3] body frame angular velocities

@dataclass
class SensorMeasurements:
    """Complete sensor measurement data"""
    time: Dict[str, np.ndarray]  # Time vectors for each sensor
    accelerometer: np.ndarray    # [N, 3] body frame
    gyroscope: np.ndarray       # [N, 3] body frame
    magnetometer: np.ndarray    # [N, 3] body frame
    gps_position: np.ndarray    # [N, 3] LLA format
    gps_velocity: np.ndarray    # [N, 3] NED frame

@dataclass
class SimulationData:
    """Complete simulation dataset"""
    ground_truth: GroundTruth
    sensor_measurements: SensorMeasurements
    configuration: Dict[str, Any]

class TrajectoryGenerator:
    """Generates ground truth trajectories"""
    
    def __init__(self):
        pass
    
    def generate_trajectory(self, config: TrajectoryConfig) -> GroundTruth:
        """Generate ground truth trajectory based on configuration"""
        
        # Create time vector
        dt = 1.0 / config.sampling_rate
        time = np.arange(0, config.duration, dt)
        n_points = len(time)
        
        # Generate trajectory based on type
        if config.trajectory_type == "CIRCULAR":
            return self._generate_circular(time, config)
        elif config.trajectory_type == "FIGURE8":
            return self._generate_figure8(time, config)
        elif config.trajectory_type == "SQUARE":
            return self._generate_square(time, config)
        elif config.trajectory_type == "STRAIGHT":
            return self._generate_straight(time, config)
        elif config.trajectory_type == "SPIRAL":
            return self._generate_spiral(time, config)
        elif config.trajectory_type == "SURVEY_LINES":
            return self._generate_survey_lines(time, config)
        elif config.trajectory_type == "COASTAL_PATROL":
            return self._generate_coastal_patrol(time, config)
        else:
            logger.warning(f"Unknown trajectory type: {config.trajectory_type}")
            return self._generate_circular(time, config)
    
    def _generate_circular(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate circular trajectory"""
        n_points = len(time)
        
        # Angular velocity for constant speed
        omega = config.speed / config.radius
        
        # Positions in local NED frame
        angles = omega * time
        north = config.radius * np.cos(angles)
        east = config.radius * np.sin(angles)
        down = np.zeros(n_points)  # Constant altitude
        
        position = np.column_stack([north, east, down])
        
        # Velocities
        vel_north = -config.radius * omega * np.sin(angles)
        vel_east = config.radius * omega * np.cos(angles)
        vel_down = np.zeros(n_points)
        
        velocity = np.column_stack([vel_north, vel_east, vel_down])
        
        # Accelerations (centripetal)
        acc_north = -config.radius * omega**2 * np.cos(angles)
        acc_east = -config.radius * omega**2 * np.sin(angles)
        acc_down = np.zeros(n_points)
        
        acceleration = np.column_stack([acc_north, acc_east, acc_down])
        
        # Orientation (heading aligned with velocity)
        headings = np.arctan2(vel_east, vel_north)
        orientation = np.zeros((n_points, 4))
        for i, heading in enumerate(headings):
            r = Rotation.from_euler('z', heading, degrees=False)
            quat = r.as_quat()  # [x, y, z, w]
            orientation[i] = [quat[3], quat[0], quat[1], quat[2]]  # [w, x, y, z]
        
        # Angular velocity (constant turn rate)
        angular_velocity = np.zeros((n_points, 3))
        angular_velocity[:, 2] = omega  # Yaw rate
        
        return GroundTruth(time, position, velocity, acceleration, 
                          orientation, angular_velocity)
    
    def _generate_figure8(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate figure-8 trajectory"""
        n_points = len(time)
        
        # Parametric figure-8 (Lemniscate)
        # Period for complete figure-8
        T = 2 * np.pi * config.figure8_width / config.speed
        t_param = 2 * np.pi * time / T
        
        # Lemniscate equations
        scale_x = config.figure8_width / 2
        scale_y = config.figure8_height / 2
        
        # Parametric equations
        cos_t = np.cos(t_param)
        sin_t = np.sin(t_param)
        denominator = 1 + sin_t**2
        
        east = scale_x * cos_t / denominator
        north = scale_y * sin_t * cos_t / denominator
        down = np.zeros(n_points)
        
        position = np.column_stack([north, east, down])
        
        # Numerical derivatives for velocity and acceleration
        dt = time[1] - time[0]
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)
        
        # Orientation from velocity direction
        orientation = self._compute_orientation_from_velocity(velocity)
        
        # Angular velocity from orientation changes
        angular_velocity = self._compute_angular_velocity(orientation, dt)
        
        return GroundTruth(time, position, velocity, acceleration, 
                          orientation, angular_velocity)
    
    def _generate_square(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate square trajectory"""
        n_points = len(time)
        
        # Square perimeter and timing
        side_length = config.square_size
        perimeter = 4 * side_length
        period = perimeter / config.speed
        
        # Normalized time (0 to 1 for complete square)
        t_norm = (time % period) / period
        
        # Initialize arrays
        position = np.zeros((n_points, 3))
        
        # Square vertices (NED frame)
        vertices = np.array([
            [side_length/2, -side_length/2, 0],    # Top-left
            [side_length/2, side_length/2, 0],     # Top-right
            [-side_length/2, side_length/2, 0],    # Bottom-right
            [-side_length/2, -side_length/2, 0]    # Bottom-left
        ])
        
        for i, t in enumerate(t_norm):
            if t < 0.25:  # First side
                s = t * 4
                position[i] = vertices[0] + s * (vertices[1] - vertices[0])
            elif t < 0.5:  # Second side
                s = (t - 0.25) * 4
                position[i] = vertices[1] + s * (vertices[2] - vertices[1])
            elif t < 0.75:  # Third side
                s = (t - 0.5) * 4
                position[i] = vertices[2] + s * (vertices[3] - vertices[2])
            else:  # Fourth side
                s = (t - 0.75) * 4
                position[i] = vertices[3] + s * (vertices[0] - vertices[3])
        
        # Numerical derivatives
        dt = time[1] - time[0]
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)
        
        # Smooth trajectories
        for j in range(3):
            velocity[:, j] = self._smooth_signal(velocity[:, j], window_size=8)
            acceleration[:, j] = self._smooth_signal(acceleration[:, j], window_size=8)
        
        # Orientation and angular velocity
        orientation = self._compute_orientation_from_velocity(velocity)
        angular_velocity = self._compute_angular_velocity(orientation, dt)
        
        return GroundTruth(time, position, velocity, acceleration, 
                          orientation, angular_velocity)
    
    def _smooth_signal(self, signal: np.ndarray, window_size: int = 5) -> np.ndarray:
        """Apply simple moving average smoothing"""
        if len(signal) < window_size:
            return signal
        
        smoothed = np.copy(signal)
        half_window = window_size // 2
        
        for i in range(half_window, len(signal) - half_window):
            smoothed[i] = np.mean(signal[i-half_window:i+half_window+1])
        
        return smoothed
    
    def _compute_orientation_from_velocity(self, velocity: np.ndarray) -> np.ndarray:
        """Compute orientation quaternions from velocity vectors"""
        n_points = velocity.shape[0]
        orientation = np.zeros((n_points, 4))
        
        for i in range(n_points):
            vel = velocity[i]
            speed = np.linalg.norm(vel)
            
            if speed > 0.1:  # Avoid division by zero
                # Heading from velocity
                heading = np.arctan2(vel[1], vel[0])  # East, North
                
                # Create rotation from heading
                r = Rotation.from_euler('z', heading, degrees=False)
                quat = r.as_quat()  # [x, y, z, w]
                orientation[i] = [quat[3], quat[0], quat[1], quat[2]]  # [w, x, y, z]
            else:
                # No movement, maintain previous orientation
                if i > 0:
                    orientation[i] = orientation[i-1]
                else:
                    orientation[i] = [1, 0, 0, 0]  # Identity quaternion
        
        return orientation
    
    def _compute_angular_velocity(self, orientation: np.ndarray, dt: float) -> np.ndarray:
        """Compute angular velocity from orientation changes"""
        n_points = orientation.shape[0]
        angular_velocity = np.zeros((n_points, 3))
        
        for i in range(1, n_points):
            # Current and previous quaternions
            q_curr = orientation[i]
            q_prev = orientation[i-1]
            
            # Relative rotation
            r_curr = Rotation.from_quat([q_curr[1], q_curr[2], q_curr[3], q_curr[0]])
            r_prev = Rotation.from_quat([q_prev[1], q_prev[2], q_prev[3], q_prev[0]])
            
            # Relative rotation
            r_rel = r_curr * r_prev.inv()
            
            # Convert to angular velocity
            rotvec = r_rel.as_rotvec()
            angular_velocity[i] = rotvec / dt
        
        # Set first point same as second
        angular_velocity[0] = angular_velocity[1] if n_points > 1 else np.zeros(3)
        
        return angular_velocity

class SensorSimulator:
    """Simulates all sensor measurements"""
    
    def __init__(self):
        self.earth_magnetic_field = np.array([20.5, -4.2, 51.8])  # µT, NED frame
        self.gravity = np.array([0, 0, 9.80665])  # m/s², NED frame
    
    def simulate_sensors(self, ground_truth: GroundTruth, 
                        sensor_config: SensorConfig, 
                        env_config: EnvironmentConfig) -> SensorMeasurements:
        """Simulate all sensor measurements"""
        
        # Generate individual sensor measurements
        accel_data = self._simulate_accelerometer(ground_truth, sensor_config)
        gyro_data = self._simulate_gyroscope(ground_truth, sensor_config)
        mag_data = self._simulate_magnetometer(ground_truth, sensor_config, env_config)
        gps_pos_data = self._simulate_gps_position(ground_truth, sensor_config, env_config)
        gps_vel_data = self._simulate_gps_velocity(ground_truth, sensor_config, env_config)
        
        # Create time vectors for each sensor
        time_vectors = {
            'accelerometer': accel_data['time'],
            'gyroscope': gyro_data['time'],
            'magnetometer': mag_data['time'],
            'gps_position': gps_pos_data['time'],
            'gps_velocity': gps_vel_data['time']
        }
        
        return SensorMeasurements(
            time=time_vectors,
            accelerometer=accel_data['measurements'],
            gyroscope=gyro_data['measurements'],
            magnetometer=mag_data['measurements'],
            gps_position=gps_pos_data['measurements'],
            gps_velocity=gps_vel_data['measurements']
        )
    
    def _simulate_accelerometer(self, ground_truth: GroundTruth, 
                               config: SensorConfig) -> Dict:
        """Simulate accelerometer measurements"""
        # Resample to accelerometer rate
        time_accel, indices = self._resample_time_vector(
            ground_truth.time, config.accelerometer_rate
        )
        
        # Get acceleration and orientation at sample points
        acc_ned = ground_truth.acceleration[indices]
        orientation = ground_truth.orientation[indices]
        
        # Transform to body frame and add gravity
        acc_body = np.zeros_like(acc_ned)
        for i, (acc, quat) in enumerate(zip(acc_ned, orientation)):
            # Rotate acceleration to body frame
            r = Rotation.from_quat([quat[1], quat[2], quat[3], quat[0]])
            acc_body[i] = r.apply(acc + self.gravity, inverse=True)
        
        # Add noise
        noise = np.random.normal(0, config.accelerometer_noise, acc_body.shape)
        acc_body += noise
        
        return {'time': time_accel, 'measurements': acc_body}
    
    def _simulate_gyroscope(self, ground_truth: GroundTruth, 
                           config: SensorConfig) -> Dict:
        """Simulate gyroscope measurements"""
        # Resample to gyroscope rate
        time_gyro, indices = self._resample_time_vector(
            ground_truth.time, config.gyroscope_rate
        )
        
        # Get angular velocity at sample points
        omega_body = ground_truth.angular_velocity[indices]
        
        # Add noise
        noise = np.random.normal(0, config.gyroscope_noise, omega_body.shape)
        omega_body += noise
        
        return {'time': time_gyro, 'measurements': omega_body}
    
    def _simulate_magnetometer(self, ground_truth: GroundTruth, 
                              config: SensorConfig, 
                              env_config: EnvironmentConfig) -> Dict:
        """Simulate magnetometer measurements"""
        # Resample to magnetometer rate
        time_mag, indices = self._resample_time_vector(
            ground_truth.time, config.magnetometer_rate
        )
        
        # Get orientation at sample points
        orientation = ground_truth.orientation[indices]
        
        # Transform magnetic field to body frame
        mag_body = np.zeros((len(indices), 3))
        for i, quat in enumerate(orientation):
            r = Rotation.from_quat([quat[1], quat[2], quat[3], quat[0]])
            mag_body[i] = r.apply(self.earth_magnetic_field, inverse=True)
        
        # Add noise
        noise = np.random.normal(0, config.magnetometer_noise, mag_body.shape)
        mag_body += noise
        
        return {'time': time_mag, 'measurements': mag_body}
    
    def _simulate_gps_position(self, ground_truth: GroundTruth, 
                              config: SensorConfig, 
                              env_config: EnvironmentConfig) -> Dict:
        """Simulate GPS position measurements"""
        # Resample to GPS rate
        time_gps, indices = self._resample_time_vector(
            ground_truth.time, config.gps_position_rate
        )
        
        # Get positions at sample points
        pos_ned = ground_truth.position[indices]
        
        # Convert to LLA (simplified conversion)
        pos_lla = self._ned_to_lla(pos_ned)
        
        # Add GPS noise
        noise_horizontal = np.random.normal(0, env_config.gps_horizontal_accuracy, 
                                          (len(indices), 2))
        noise_vertical = np.random.normal(0, env_config.gps_vertical_accuracy, 
                                        len(indices))
        
        pos_lla[:, :2] += noise_horizontal * 1e-5  # Convert to degrees (rough)
        pos_lla[:, 2] += noise_vertical
        
        # Apply GPS outages
        if env_config.gps_outage_probability > 0:
            outage_mask = np.random.random(len(indices)) < env_config.gps_outage_probability
            pos_lla[outage_mask] = np.nan
        
        return {'time': time_gps, 'measurements': pos_lla}
    
    def _simulate_gps_velocity(self, ground_truth: GroundTruth, 
                              config: SensorConfig, 
                              env_config: EnvironmentConfig) -> Dict:
        """Simulate GPS velocity measurements"""
        # Resample to GPS velocity rate
        time_gps, indices = self._resample_time_vector(
            ground_truth.time, config.gps_velocity_rate
        )
        
        # Get velocities at sample points
        vel_ned = ground_truth.velocity[indices]
        
        # Add noise
        noise = np.random.normal(0, env_config.gps_velocity_accuracy, vel_ned.shape)
        vel_ned += noise
        
        # Apply GPS outages
        if env_config.gps_outage_probability > 0:
            outage_mask = np.random.random(len(indices)) < env_config.gps_outage_probability
            vel_ned[outage_mask] = np.nan
        
        return {'time': time_gps, 'measurements': vel_ned}
    
    def _resample_time_vector(self, original_time: np.ndarray, 
                             target_rate: float) -> Tuple[np.ndarray, np.ndarray]:
        """Resample time vector to target rate"""
        dt_target = 1.0 / target_rate
        duration = original_time[-1] - original_time[0]
        
        # New time vector
        new_time = np.arange(original_time[0], original_time[-1], dt_target)
        
        # Find closest indices in original time
        indices = np.searchsorted(original_time, new_time)
        indices = np.clip(indices, 0, len(original_time) - 1)
        
        return new_time, indices
    
    def _ned_to_lla(self, pos_ned: np.ndarray, 
                    ref_lat: float = 42.3601, 
                    ref_lon: float = -71.0589, 
                    ref_alt: float = 0.0) -> np.ndarray:
        """Convert NED to LLA coordinates (simplified)"""
        # Earth radius approximation
        R_earth = 6378137.0  # meters
        
        pos_lla = np.zeros_like(pos_ned)
        
        # Convert NED to LLA
        pos_lla[:, 0] = ref_lat + np.degrees(pos_ned[:, 0] / R_earth)  # Latitude
        pos_lla[:, 1] = ref_lon + np.degrees(pos_ned[:, 1] / (R_earth * np.cos(np.radians(ref_lat))))  # Longitude
        pos_lla[:, 2] = ref_alt - pos_ned[:, 2]  # Altitude
        
        return pos_lla

class SimulationEngine:
    """Main simulation orchestrator"""
    
    def __init__(self):
        self.trajectory_generator = TrajectoryGenerator()
        self.sensor_simulator = SensorSimulator()
    
    def run_simulation(self, traj_config: TrajectoryConfig, 
                      sensor_config: SensorConfig, 
                      env_config: EnvironmentConfig) -> SimulationData:
        """Run complete simulation"""
        
        logger.info("Starting trajectory generation...")
        ground_truth = self.trajectory_generator.generate_trajectory(traj_config)
        
        logger.info("Starting sensor simulation...")
        sensor_measurements = self.sensor_simulator.simulate_sensors(
            ground_truth, sensor_config, env_config
        )
        
        # Package configuration
        config_dict = {
            'trajectory': traj_config,
            'sensors': sensor_config,
            'environment': env_config
        }
        
        logger.info("Simulation completed successfully")
        return SimulationData(ground_truth, sensor_measurements, config_dict)
'''

    with open("simulation/simulation_engine.py", "w") as f:
        f.write(sim_engine_content)

def generate_fusion_engine():
    """Generate fusion engine and integrate ProductionINSFilter"""
    
    # Copy the original INS filter
    ins_filter_content = '''#!/usr/bin/env python3
"""
Production GPS-INS Fusion Filter - Complete 28-State Extended Kalman Filter

This is a complete, production-ready implementation of a GPS/INS fusion filter
that serves as a drop-in replacement for MATLAB's insfilterAsync.

Features:
- 28-state continuous-discrete Extended Kalman Filter
- Complete mathematical derivations for all Jacobians
- Proper quaternion kinematics and geodetic transformations
- General-purpose API with configurable parameters
- No hardcoded values or simplifications


"""

import numpy as np
from scipy.spatial.transform import Rotation
from scipy.linalg import expm, block_diag
from typing import Optional, Tuple, Dict, Union, List
from dataclasses import dataclass, field
import warnings

@dataclass
class INSFilterConfig:
    """Complete configuration for the INS filter"""
    
    # Process noise standard deviations
    quaternion_noise: float = 1e-2
    angular_velocity_noise: float = 100.0
    acceleration_noise: float = 100.0
    accelerometer_bias_noise: float = 1e-7
    gyroscope_bias_noise: float = 1e-7
    geomagnetic_field_noise: float = 1e-7
    magnetometer_bias_noise: float = 1e-7
    
    # Initial state covariance diagonal values
    quaternion_covariance: float = 1e-3
    angular_velocity_covariance: float = 1e-2
    position_covariance: float = 1e-3
    velocity_covariance: float = 1e-3
    acceleration_covariance: float = 1e-2
    accelerometer_bias_covariance: float = 1e-4
    gyroscope_bias_covariance: float = 1e-4
    geomagnetic_field_covariance: float = 1e-2
    magnetometer_bias_covariance: float = 1e-4
    
    # Earth model parameters
    earth_rotation_rate: float = 7.2921159e-5  # rad/s
    earth_equatorial_radius: float = 6378137.0  # meters (WGS84)
    earth_flattening: float = 1.0 / 298.257223563  # WGS84
    gravity_magnitude: float = 9.80665  # m/s² (standard gravity)
    
    # Numerical integration parameters
    max_dt: float = 0.1  # Maximum time step for integration
    quaternion_norm_threshold: float = 1e-6  # Threshold for quaternion normalization

class ProductionINSFilter:
    """
    Production-grade 28-state GPS/INS Extended Kalman Filter
    
    State Vector (28 elements):
    [0:4]   - Quaternion (w, x, y, z) - orientation from navigation to body frame
    [4:7]   - Angular velocity (rad/s) - body frame
    [7:10]  - Position NED (m) - navigation frame  
    [10:13] - Velocity NED (m/s) - navigation frame
    [13:16] - Acceleration NED (m/s²) - navigation frame
    [16:19] - Accelerometer bias (m/s²) - body frame
    [19:22] - Gyroscope bias (rad/s) - body frame
    [22:25] - Geomagnetic field NED (µT) - navigation frame
    [25:28] - Magnetometer bias (µT) - body frame
    """
    
    def __init__(self, 
                 reference_location: Tuple[float, float, float],
                 config: Optional[INSFilterConfig] = None):
        """
        Initialize the INS filter
        
        Args:
            reference_location: (latitude, longitude, altitude) in degrees and meters
            config: Filter configuration parameters
        """
        self.config = config or INSFilterConfig()
        
        # Reference location for NED frame
        self.ref_latitude = np.radians(reference_location[0])
        self.ref_longitude = np.radians(reference_location[1])
        self.ref_altitude = reference_location[2]
        
        # Precompute reference location parameters
        self._compute_reference_parameters()
        
        # Initialize state vector (28 elements)
        self.state = np.zeros(28)
        self.state[0] = 1.0  # Initialize with identity quaternion
        
        # Initialize state covariance matrix
        self.P = self._initialize_covariance_matrix()
        
        # Prediction timestamp for dt calculation
        self.last_prediction_time = None
        
        # State indices for clarity
        self.QUAT_IDX = slice(0, 4)
        self.OMEGA_IDX = slice(4, 7)
        self.POS_IDX = slice(7, 10)
        self.VEL_IDX = slice(10, 13)
        self.ACC_IDX = slice(13, 16)
        self.ACC_BIAS_IDX = slice(16, 19)
        self.GYRO_BIAS_IDX = slice(19, 22)
        self.MAG_FIELD_IDX = slice(22, 25)
        self.MAG_BIAS_IDX = slice(25, 28)
    
    def _compute_reference_parameters(self):
        """Compute reference location dependent parameters"""
        lat = self.ref_latitude
        
        # Earth model parameters
        a = self.config.earth_equatorial_radius
        f = self.config.earth_flattening
        e2 = 2*f - f**2  # First eccentricity squared
        
        # Radius of curvature in meridian
        self.M = a * (1 - e2) / (1 - e2 * np.sin(lat)**2)**(3/2)
        
        # Radius of curvature in prime vertical
        self.N = a / np.sqrt(1 - e2 * np.sin(lat)**2)
        
        # Local gravity (simplified model)
        self.local_gravity = self.config.gravity_magnitude * (
            1 + 5.3024e-3 * np.sin(lat)**2 - 5.8e-6 * np.sin(2*lat)**2
        )
        
        # Transport rate (Earth rotation rate components in NED)
        omega_ie = self.config.earth_rotation_rate
        self.omega_ie_ned = np.array([
            omega_ie * np.cos(lat),  # North
            0.0,                     # East  
            -omega_ie * np.sin(lat)  # Down
        ])
    
    def _initialize_covariance_matrix(self) -> np.ndarray:
        """Initialize the state covariance matrix"""
        P_diag = np.array([
            # Quaternion
            self.config.quaternion_covariance,
            self.config.quaternion_covariance,
            self.config.quaternion_covariance,
            self.config.quaternion_covariance,
            # Angular velocity
            self.config.angular_velocity_covariance,
            self.config.angular_velocity_covariance,
            self.config.angular_velocity_covariance,
            # Position
            self.config.position_covariance,
            self.config.position_covariance,
            self.config.position_covariance,
            # Velocity
            self.config.velocity_covariance,
            self.config.velocity_covariance,
            self.config.velocity_covariance,
            # Acceleration
            self.config.acceleration_covariance,
            self.config.acceleration_covariance,
            self.config.acceleration_covariance,
            # Accelerometer bias
            self.config.accelerometer_bias_covariance,
            self.config.accelerometer_bias_covariance,
            self.config.accelerometer_bias_covariance,
            # Gyroscope bias
            self.config.gyroscope_bias_covariance,
            self.config.gyroscope_bias_covariance,
            self.config.gyroscope_bias_covariance,
            # Geomagnetic field
            self.config.geomagnetic_field_covariance,
            self.config.geomagnetic_field_covariance,
            self.config.geomagnetic_field_covariance,
            # Magnetometer bias
            self.config.magnetometer_bias_covariance,
            self.config.magnetometer_bias_covariance,
            self.config.magnetometer_bias_covariance
        ])
        
        return np.diag(P_diag)
    
    def predict(self, dt: float, timestamp: Optional[float] = None):
        """
        Predict state forward using continuous-discrete EKF
        
        Args:
            dt: Time step in seconds
            timestamp: Optional timestamp for automatic dt calculation
        """
        if timestamp is not None and self.last_prediction_time is not None:
            dt = timestamp - self.last_prediction_time
        
        # Clamp dt to maximum value for numerical stability
        dt = min(dt, self.config.max_dt)
        
        if dt <= 0:
            warnings.warn("Non-positive time step in prediction")
            return
        
        # Extract current state components
        q = self.state[self.QUAT_IDX].copy()
        omega = self.state[self.OMEGA_IDX].copy()
        pos = self.state[self.POS_IDX].copy()
        vel = self.state[self.VEL_IDX].copy()
        acc = self.state[self.ACC_IDX].copy()
        
        # Normalize quaternion
        q = self._normalize_quaternion(q)
        self.state[self.QUAT_IDX] = q
        
        # Quaternion propagation using exact integration
        self.state[self.QUAT_IDX] = self._propagate_quaternion(q, omega, dt)
        
        # Position propagation (second-order integration)
        self.state[self.POS_IDX] = pos + vel * dt + 0.5 * acc * dt**2
        
        # Velocity propagation (first-order integration)
        self.state[self.VEL_IDX] = vel + acc * dt
        
        # Biases and other states remain constant (random walk model)
        
        # Compute state transition matrix F
        F = self._compute_state_transition_matrix(dt)
        
        # Compute process noise covariance Q
        Q = self._compute_process_noise_matrix(dt)
        
        # Propagate covariance: P = F*P*F' + Q
        self.P = F @ self.P @ F.T + Q
        
        # Ensure covariance symmetry
        self.P = 0.5 * (self.P + self.P.T)
        
        # Update timestamp
        if timestamp is not None:
            self.last_prediction_time = timestamp
    
    def _normalize_quaternion(self, q: np.ndarray) -> np.ndarray:
        """Normalize quaternion with numerical stability check"""
        norm = np.linalg.norm(q)
        if norm < self.config.quaternion_norm_threshold:
            warnings.warn("Quaternion norm too small, resetting to identity")
            return np.array([1.0, 0.0, 0.0, 0.0])
        return q / norm
    
    def _propagate_quaternion(self, q: np.ndarray, omega: np.ndarray, dt: float) -> np.ndarray:
        """
        Exact quaternion propagation using matrix exponential
        
        Args:
            q: Current quaternion [w, x, y, z]
            omega: Angular velocity [wx, wy, wz] in rad/s
            dt: Time step in seconds
            
        Returns:
            Propagated quaternion
        """
        omega_norm = np.linalg.norm(omega)
        
        if omega_norm < 1e-8:
            # No rotation case
            return self._normalize_quaternion(q)
        
        # Quaternion kinematics matrix
        Omega = np.array([
            [0,        -omega[0], -omega[1], -omega[2]],
            [omega[0],  0,         omega[2], -omega[1]],
            [omega[1], -omega[2],  0,         omega[0]],
            [omega[2],  omega[1], -omega[0],  0       ]
        ])
        
        # Exact solution using matrix exponential
        q_dot_matrix = 0.5 * Omega
        q_new = expm(q_dot_matrix * dt) @ q
        
        return self._normalize_quaternion(q_new)
    
    def _compute_state_transition_matrix(self, dt: float) -> np.ndarray:
        """
        Compute the complete 28x28 state transition matrix F
        
        This includes all cross-coupling terms and proper derivatives
        """
        F = np.eye(28)
        
        # Extract state components
        q = self.state[self.QUAT_IDX]
        omega = self.state[self.OMEGA_IDX]
        
        # Quaternion derivatives w.r.t. angular velocity
        F[0:4, 4:7] = self._quaternion_omega_jacobian(q, omega, dt)
        
        # Position derivatives
        F[7:10, 10:13] = np.eye(3) * dt      # ∂pos/∂vel
        F[7:10, 13:16] = np.eye(3) * dt**2 / 2  # ∂pos/∂acc
        
        # Velocity derivatives  
        F[10:13, 13:16] = np.eye(3) * dt     # ∂vel/∂acc
        
        # Angular velocity derivatives w.r.t. gyroscope bias
        F[4:7, 19:22] = -np.eye(3)           # ∂omega/∂gyro_bias
        
        return F
    
    def _quaternion_omega_jacobian(self, q: np.ndarray, omega: np.ndarray, dt: float) -> np.ndarray:
        """
        Compute Jacobian of quaternion propagation w.r.t. angular velocity
        
        This is the exact derivative of the matrix exponential solution
        """
        omega_norm = np.linalg.norm(omega)
        
        if omega_norm < 1e-8:
            # Small angle approximation
            return 0.5 * dt * np.array([
                [-q[1], -q[2], -q[3]],
                [ q[0], -q[3],  q[2]],
                [ q[3],  q[0], -q[1]],
                [-q[2],  q[1],  q[0]]
            ])
        
        # Full nonlinear Jacobian (this is complex - using approximation for now)
        # In practice, this requires careful mathematical derivation
        Omega_q = 0.5 * np.array([
            [-q[1], -q[2], -q[3]],
            [ q[0], -q[3],  q[2]],
            [ q[3],  q[0], -q[1]],
            [-q[2],  q[1],  q[0]]
        ])
        
        return Omega_q * dt
    
    def _compute_process_noise_matrix(self, dt: float) -> np.ndarray:
        """Compute process noise covariance matrix Q"""
        Q = np.zeros((28, 28))
        
        # Quaternion process noise
        Q[0:4, 0:4] = np.eye(4) * self.config.quaternion_noise**2 * dt
        
        # Angular velocity process noise
        Q[4:7, 4:7] = np.eye(3) * self.config.angular_velocity_noise**2 * dt
        
        # Position process noise (integrated from acceleration)
        acc_var = self.config.acceleration_noise**2
        Q[7:10, 7:10] = np.eye(3) * acc_var * dt**3 / 3
        Q[7:10, 10:13] = np.eye(3) * acc_var * dt**2 / 2
        Q[10:13, 7:10] = np.eye(3) * acc_var * dt**2 / 2
        Q[10:13, 10:13] = np.eye(3) * acc_var * dt
        
        # Acceleration process noise
        Q[13:16, 13:16] = np.eye(3) * acc_var * dt
        
        # Bias process noises (random walk)
        Q[16:19, 16:19] = np.eye(3) * self.config.accelerometer_bias_noise**2 * dt
        Q[19:22, 19:22] = np.eye(3) * self.config.gyroscope_bias_noise**2 * dt
        Q[22:25, 22:25] = np.eye(3) * self.config.geomagnetic_field_noise**2 * dt
        Q[25:28, 25:28] = np.eye(3) * self.config.magnetometer_bias_noise**2 * dt
        
        return Q
    
    def fuse_accelerometer(self, 
                          measurement: np.ndarray, 
                          noise_variance: Union[float, np.ndarray]):
        """
        Fuse 3-axis accelerometer measurement
        
        Args:
            measurement: Accelerometer reading [ax, ay, az] in m/s² (body frame)
            noise_variance: Measurement noise variance (scalar or 3x3 matrix)
        """
        # Extract state components
        q = self.state[self.QUAT_IDX]
        acc_ned = self.state[self.ACC_IDX]
        acc_bias = self.state[self.ACC_BIAS_IDX]
        
        # Expected measurement: gravity + acceleration - bias (all in body frame)
        gravity_ned = np.array([0, 0, self.local_gravity])
        gravity_body = self._rotate_ned_to_body(gravity_ned, q)
        acc_body = self._rotate_ned_to_body(acc_ned, q)
        
        h = gravity_body + acc_body - acc_bias
        
        # Innovation
        innovation = measurement - h
        
        # Measurement Jacobian H (3x28)
        H = np.zeros((3, 28))
        
        # ∂h/∂quaternion (gravity and acceleration rotation)
        H[0:3, 0:4] = self._gravity_quaternion_jacobian(q, gravity_ned + acc_ned)
        
        # ∂h/∂acceleration (rotation from NED to body)
        H[0:3, 13:16] = self._ned_to_body_rotation_matrix(q)
        
        # ∂h/∂accelerometer_bias
        H[0:3, 16:19] = -np.eye(3)
        
        # Measurement noise covariance
        if np.isscalar(noise_variance):
            R = np.eye(3) * noise_variance
        else:
            R = np.atleast_2d(noise_variance)
        
        # Kalman update
        self._kalman_update(innovation, H, R)
    
    def fuse_gyroscope(self, 
                      measurement: np.ndarray, 
                      noise_variance: Union[float, np.ndarray]):
        """
        Fuse 3-axis gyroscope measurement
        
        Args:
            measurement: Gyroscope reading [wx, wy, wz] in rad/s (body frame)
            noise_variance: Measurement noise variance (scalar or 3x3 matrix)
        """
        # Extract state components
        omega = self.state[self.OMEGA_IDX]
        gyro_bias = self.state[self.GYRO_BIAS_IDX]
        
        # Expected measurement: angular velocity - bias
        h = omega - gyro_bias
        
        # Innovation
        innovation = measurement - h
        
        # Measurement Jacobian H (3x28)
        H = np.zeros((3, 28))
        H[0:3, 4:7] = np.eye(3)     # ∂h/∂omega
        H[0:3, 19:22] = -np.eye(3)  # ∂h/∂gyro_bias
        
        # Measurement noise covariance
        if np.isscalar(noise_variance):
            R = np.eye(3) * noise_variance
        else:
            R = np.atleast_2d(noise_variance)
        
        # Kalman update
        self._kalman_update(innovation, H, R)
    
    def fuse_magnetometer(self, 
                         measurement: np.ndarray, 
                         noise_variance: Union[float, np.ndarray]):
        """
        Fuse 3-axis magnetometer measurement
        
        Args:
            measurement: Magnetometer reading [mx, my, mz] in µT (body frame)
            noise_variance: Measurement noise variance (scalar or 3x3 matrix)
        """
        # Extract state components
        q = self.state[self.QUAT_IDX]
        mag_field_ned = self.state[self.MAG_FIELD_IDX]
        mag_bias = self.state[self.MAG_BIAS_IDX]
        
        # Expected measurement: magnetic field rotated to body frame - bias
        mag_body = self._rotate_ned_to_body(mag_field_ned, q)
        h = mag_body - mag_bias
        
        # Innovation
        innovation = measurement - h
        
        # Measurement Jacobian H (3x28)
        H = np.zeros((3, 28))
        
        # ∂h/∂quaternion (magnetic field rotation)
        H[0:3, 0:4] = self._magnetometer_quaternion_jacobian(q, mag_field_ned)
        
        # ∂h/∂magnetic_field (rotation from NED to body)
        H[0:3, 22:25] = self._ned_to_body_rotation_matrix(q)
        
        # ∂h/∂magnetometer_bias
        H[0:3, 25:28] = -np.eye(3)
        
        # Measurement noise covariance
        if np.isscalar(noise_variance):
            R = np.eye(3) * noise_variance
        else:
            R = np.atleast_2d(noise_variance)
        
        # Kalman update
        self._kalman_update(innovation, H, R)
    
    def fuse_gps_position(self, 
                         lla_measurement: np.ndarray, 
                         noise_variance: Union[float, np.ndarray]):
        """
        Fuse GPS position measurement
        
        Args:
            lla_measurement: GPS position [lat, lon, alt] in degrees and meters
            noise_variance: Measurement noise variance (scalar or 3x3 matrix)
        """
        # Convert LLA to NED
        pos_ned_measured = self._lla_to_ned(lla_measurement)
        
        # Extract state position
        pos_ned_state = self.state[self.POS_IDX]
        
        # Expected measurement is simply the state position
        h = pos_ned_state
        
        # Innovation
        innovation = pos_ned_measured - h
        
        # Measurement Jacobian H (3x28)
        H = np.zeros((3, 28))
        H[0:3, 7:10] = np.eye(3)  # ∂h/∂position
        
        # Measurement noise covariance
        if np.isscalar(noise_variance):
            R = np.eye(3) * noise_variance
        else:
            R = np.atleast_2d(noise_variance)
        
        # Kalman update
        self._kalman_update(innovation, H, R)
    
    def fuse_gps_velocity(self, 
                         velocity_measurement: np.ndarray, 
                         noise_variance: Union[float, np.ndarray]):
        """
        Fuse GPS velocity measurement
        
        Args:
            velocity_measurement: GPS velocity [vn, ve, vd] in m/s (NED frame)
            noise_variance: Measurement noise variance (scalar or 3x3 matrix)
        """
        # Extract state velocity
        vel_ned_state = self.state[self.VEL_IDX]
        
        # Expected measurement is simply the state velocity
        h = vel_ned_state
        
        # Innovation
        innovation = velocity_measurement - h
        
        # Measurement Jacobian H (3x28)
        H = np.zeros((3, 28))
        H[0:3, 10:13] = np.eye(3)  # ∂h/∂velocity
        
        # Measurement noise covariance
        if np.isscalar(noise_variance):
            R = np.eye(3) * noise_variance
        else:
            R = np.atleast_2d(noise_variance)
        
        # Kalman update
        self._kalman_update(innovation, H, R)
    
    def _kalman_update(self, innovation: np.ndarray, H: np.ndarray, R: np.ndarray):
        """
        Perform Kalman filter update step
        
        Args:
            innovation: Measurement innovation vector
            H: Measurement Jacobian matrix
            R: Measurement noise covariance matrix
        """
        # Innovation covariance
        S = H @ self.P @ H.T + R
        
        # Kalman gain
        try:
            K = self.P @ H.T @ np.linalg.inv(S)
        except np.linalg.LinAlgError:
            warnings.warn("Singular innovation covariance matrix")
            return
        
        # State update
        self.state += K @ innovation
        
        # Normalize quaternion after update
        self.state[self.QUAT_IDX] = self._normalize_quaternion(self.state[self.QUAT_IDX])
        
        # Covariance update (Joseph form for numerical stability)
        I_KH = np.eye(28) - K @ H
        self.P = I_KH @ self.P @ I_KH.T + K @ R @ K.T
        
        # Ensure symmetry
        self.P = 0.5 * (self.P + self.P.T)
    
    def _gravity_quaternion_jacobian(self, q: np.ndarray, vector_ned: np.ndarray) -> np.ndarray:
        """
        Compute Jacobian of vector rotation (NED to body) w.r.t. quaternion
        
        For vector v_body = R(q) * v_ned, compute ∂v_body/∂q
        """
        w, x, y, z = q
        vx, vy, vz = vector_ned
        
        # Analytical derivative of rotation matrix elements w.r.t. quaternion
        jacobian = 2 * np.array([
            # ∂v_body_x/∂q
            [w*vx + z*vy - y*vz, x*vx + y*vy + z*vz, -y*vx + x*vy + w*vz, -z*vx - w*vy + x*vz],
            # ∂v_body_y/∂q  
            [-z*vx + w*vy + x*vz, y*vx - x*vy - w*vz, x*vx + y*vy + z*vz, w*vx + z*vy - y*vz],
            # ∂v_body_z/∂q
            [y*vx - x*vz + w*vz, z*vx + w*vy - x*vz, -w*vx - z*vy + y*vz, x*vx + y*vy + z*vz]
        ])
        
        return jacobian
    
    def _magnetometer_quaternion_jacobian(self, q: np.ndarray, mag_field_ned: np.ndarray) -> np.ndarray:
        """
        Compute Jacobian of magnetic field rotation w.r.t. quaternion
        Same as gravity Jacobian but for magnetic field vector
        """
        return self._gravity_quaternion_jacobian(q, mag_field_ned)
    
    def _rotate_ned_to_body(self, vector_ned: np.ndarray, q: np.ndarray) -> np.ndarray:
        """Rotate vector from NED frame to body frame using quaternion"""
        rotation = Rotation.from_quat([q[1], q[2], q[3], q[0]])  # scipy uses [x,y,z,w]
        return rotation.apply(vector_ned, inverse=True)
    
    def _ned_to_body_rotation_matrix(self, q: np.ndarray) -> np.ndarray:
        """Get rotation matrix from NED to body frame"""
        rotation = Rotation.from_quat([q[1], q[2], q[3], q[0]])  # scipy uses [x,y,z,w]
        return rotation.as_matrix().T  # Transpose for NED to body
    
    def _lla_to_ned(self, lla: np.ndarray) -> np.ndarray:
        """
        Convert geodetic coordinates to NED position
        
        Args:
            lla: [latitude, longitude, altitude] in degrees and meters
            
        Returns:
            NED position [north, east, down] in meters
        """
        lat_rad = np.radians(lla[0])
        lon_rad = np.radians(lla[1])
        alt = lla[2]
        
        # Differences from reference
        dlat = lat_rad - self.ref_latitude
        dlon = lon_rad - self.ref_longitude
        dalt = alt - self.ref_altitude
        
        # Convert to NED (using small angle approximation for efficiency)
        # For precise conversion, use full geodetic formulas
        north = dlat * (self.M + self.ref_altitude)
        east = dlon * (self.N + self.ref_altitude) * np.cos(self.ref_latitude)
        down = -dalt
        
        return np.array([north, east, down])
    
    def get_state(self) -> np.ndarray:
        """Get complete state vector"""
        return self.state.copy()
    
    def get_position_ned(self) -> np.ndarray:
        """Get position in NED frame [north, east, down] in meters"""
        return self.state[self.POS_IDX].copy()
    
    def get_velocity_ned(self) -> np.ndarray:
        """Get velocity in NED frame [vn, ve, vd] in m/s"""
        return self.state[self.VEL_IDX].copy()
    
    def get_acceleration_ned(self) -> np.ndarray:
        """Get acceleration in NED frame [an, ae, ad] in m/s²"""
        return self.state[self.ACC_IDX].copy()
    
    def get_orientation_quaternion(self) -> np.ndarray:
        """Get orientation quaternion [w, x, y, z] (navigation to body)"""
        return self._normalize_quaternion(self.state[self.QUAT_IDX].copy())
    
    def get_angular_velocity(self) -> np.ndarray:
        """Get angular velocity [wx, wy, wz] in rad/s (body frame)"""
        return self.state[self.OMEGA_IDX].copy()
    
    def get_sensor_biases(self) -> Dict[str, np.ndarray]:
        """Get all sensor biases"""
        return {
            'accelerometer': self.state[self.ACC_BIAS_IDX].copy(),
            'gyroscope': self.state[self.GYRO_BIAS_IDX].copy(), 
            'magnetometer': self.state[self.MAG_BIAS_IDX].copy()
        }
    
    def get_geomagnetic_field(self) -> np.ndarray:
        """Get estimated geomagnetic field in NED frame [mx, my, mz] in µT"""
        return self.state[self.MAG_FIELD_IDX].copy()
    
    def get_covariance_matrix(self) -> np.ndarray:
        """Get state covariance matrix"""
        return self.P.copy()
    
    def set_state(self, state: np.ndarray):
        """Set complete state vector"""
        if len(state) != 28:
            raise ValueError("State vector must have 28 elements")
        self.state = state.copy()
        self.state[self.QUAT_IDX] = self._normalize_quaternion(self.state[self.QUAT_IDX])
    
    def set_geomagnetic_field(self, magnetic_field_ned: np.ndarray):
        """Set geomagnetic field vector in NED frame [mx, my, mz] in µT"""
        self.state[self.MAG_FIELD_IDX] = magnetic_field_ned.copy()
    
    def reset_biases(self):
        """Reset all sensor biases to zero"""
        self.state[self.ACC_BIAS_IDX] = 0.0
        self.state[self.GYRO_BIAS_IDX] = 0.0  
        self.state[self.MAG_BIAS_IDX] = 0.0
'''
    
    with open("fusion/saeid_gps_ins_ekf.py", "w") as f:
        f.write(ins_filter_content)
    
    # Fusion Engine
    fusion_engine_content = '''"""Fusion Engine - INS Filter Integration and Management"""
import numpy as np
import logging
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
from fusion.saeid_gps_ins_ekf import ProductionINSFilter, INSFilterConfig
from config.configuration_manager import SensorConfig
from simulation.simulation_engine import SimulationData, SensorMeasurements

logger = logging.getLogger(__name__)

@dataclass
class EstimationResults:
    """INS filter estimation results"""
    time: np.ndarray
    position: np.ndarray      # [N, 3] NED coordinates
    velocity: np.ndarray      # [N, 3] NED velocities  
    acceleration: np.ndarray  # [N, 3] NED accelerations
    orientation: np.ndarray   # [N, 4] quaternions [w, x, y, z]
    angular_velocity: np.ndarray  # [N, 3] body frame angular velocities
    sensor_biases: Dict[str, np.ndarray]  # Estimated sensor biases
    covariance_trace: np.ndarray  # Trace of covariance matrix over time

class FusionEngine:
    """Main fusion engine that integrates with ProductionINSFilter"""
    
    def __init__(self):
        self.ins_filter = None
        self.estimation_results = None
    
    def run_fusion(self, simulation_data: SimulationData, 
                  sensor_config: SensorConfig) -> EstimationResults:
        """
        Run complete sensor fusion using selective sensor participation
        
        Args:
            simulation_data: Complete simulation data with all sensors
            sensor_config: Configuration specifying which sensors to use
            
        Returns:
            EstimationResults containing all estimates
        """
        logger.info("Starting sensor fusion...")
        
        # Initialize filter
        self._initialize_filter(simulation_data, sensor_config)
        
        # Run fusion with selective sensor participation
        results = self._process_sensor_measurements(
            simulation_data.sensor_measurements, sensor_config
        )
        
        logger.info("Sensor fusion completed successfully")
        return results
    
    def _initialize_filter(self, simulation_data: SimulationData, 
                          sensor_config: SensorConfig):
        """Initialize the INS filter"""
        
        # Get reference location from trajectory config
        traj_config = simulation_data.configuration['trajectory']
        ref_location = (traj_config.center_lat, traj_config.center_lon, traj_config.center_alt)
        
        # Create filter configuration based on sensor quality
        filter_config = self._create_filter_config(sensor_config)
        
        # Initialize filter
        self.ins_filter = ProductionINSFilter(ref_location, filter_config)
        
        # Set initial magnetic field
        self.ins_filter.set_geomagnetic_field(np.array([20.5, -4.2, 51.8]))
        
        logger.info(f"INS filter initialized with reference location: {ref_location}")
    
    def _create_filter_config(self, sensor_config: SensorConfig) -> INSFilterConfig:
        """Create INS filter configuration from sensor configuration"""
        
        config = INSFilterConfig()
        
        # Set noise parameters based on sensor quality
        if sensor_config.imu_quality == "CONSUMER":
            config.acceleration_noise = 10.0
            config.angular_velocity_noise = 1.0
        elif sensor_config.imu_quality == "NAVIGATION":
            config.acceleration_noise = 1.0
            config.angular_velocity_noise = 0.1
        elif sensor_config.imu_quality == "SURVEY":
            config.acceleration_noise = 0.1
            config.angular_velocity_noise = 0.01
        
        return config
    
    def _process_sensor_measurements(self, sensor_measurements: SensorMeasurements,
                                   sensor_config: SensorConfig) -> EstimationResults:
        """Process sensor measurements with selective participation"""
        
        # Get all unique time stamps and sort
        all_times = []
        
        if sensor_config.use_accelerometer:
            all_times.extend(sensor_measurements.time['accelerometer'])
        if sensor_config.use_gyroscope:
            all_times.extend(sensor_measurements.time['gyroscope'])
        if sensor_config.use_magnetometer:
            all_times.extend(sensor_measurements.time['magnetometer'])
        if sensor_config.use_gps_position:
            all_times.extend(sensor_measurements.time['gps_position'])
        if sensor_config.use_gps_velocity:
            all_times.extend(sensor_measurements.time['gps_velocity'])
        
        unique_times = np.unique(all_times)
        
        # Storage for results
        n_samples = len(unique_times)
        positions = np.zeros((n_samples, 3))
        velocities = np.zeros((n_samples, 3))
        accelerations = np.zeros((n_samples, 3))
        orientations = np.zeros((n_samples, 4))
        angular_velocities = np.zeros((n_samples, 3))
        covariance_traces = np.zeros(n_samples)
        
        # Create sensor data indices for efficient lookup
        sensor_indices = self._create_sensor_indices(sensor_measurements, sensor_config)
        
        # Process each time step
        prev_time = unique_times[0]
        
        for i, current_time in enumerate(unique_times):
            # Prediction step
            dt = current_time - prev_time if i > 0 else 0.01
            self.ins_filter.predict(dt)
            
            # Update steps (fuse available measurements)
            self._fuse_measurements_at_time(current_time, sensor_measurements, 
                                          sensor_config, sensor_indices)
            
            # Store results
            positions[i] = self.ins_filter.get_position_ned()
            velocities[i] = self.ins_filter.get_velocity_ned()
            accelerations[i] = self.ins_filter.get_acceleration_ned()
            orientations[i] = self.ins_filter.get_orientation_quaternion()
            angular_velocities[i] = self.ins_filter.get_angular_velocity()
            covariance_traces[i] = np.trace(self.ins_filter.get_covariance_matrix())
            
            prev_time = current_time
        
        # Get final sensor biases
        final_biases = self.ins_filter.get_sensor_biases()
        
        return EstimationResults(
            time=unique_times,
            position=positions,
            velocity=velocities,
            acceleration=accelerations,
            orientation=orientations,
            angular_velocity=angular_velocities,
            sensor_biases=final_biases,
            covariance_trace=covariance_traces
        )
    
    def _create_sensor_indices(self, sensor_measurements: SensorMeasurements,
                              sensor_config: SensorConfig) -> Dict:
        """Create efficient lookup indices for sensor data"""
        indices = {}
        
        if sensor_config.use_accelerometer:
            indices['accelerometer'] = {
                'times': sensor_measurements.time['accelerometer'],
                'data': sensor_measurements.accelerometer,
                'noise': sensor_config.accelerometer_noise**2
            }
        
        if sensor_config.use_gyroscope:
            indices['gyroscope'] = {
                'times': sensor_measurements.time['gyroscope'],
                'data': sensor_measurements.gyroscope,
                'noise': sensor_config.gyroscope_noise**2
            }
        
        if sensor_config.use_magnetometer:
            indices['magnetometer'] = {
                'times': sensor_measurements.time['magnetometer'],
                'data': sensor_measurements.magnetometer,
                'noise': sensor_config.magnetometer_noise**2
            }
        
        if sensor_config.use_gps_position:
            indices['gps_position'] = {
                'times': sensor_measurements.time['gps_position'],
                'data': sensor_measurements.gps_position,
                'noise': sensor_config.gps_position_noise**2
            }
        
        if sensor_config.use_gps_velocity:
            indices['gps_velocity'] = {
                'times': sensor_measurements.time['gps_velocity'],
                'data': sensor_measurements.gps_velocity,
                'noise': sensor_config.gps_velocity_noise**2
            }
        
        return indices
    
    def _fuse_measurements_at_time(self, current_time: float,
                                  sensor_measurements: SensorMeasurements,
                                  sensor_config: SensorConfig,
                                  sensor_indices: Dict):
        """Fuse all available measurements at current time"""
        
        tolerance = 1e-6  # Time tolerance for finding measurements
        
        # Accelerometer
        if 'accelerometer' in sensor_indices:
            data_info = sensor_indices['accelerometer']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_accelerometer(
                    data_info['data'][idx], data_info['noise']
                )
        
        # Gyroscope
        if 'gyroscope' in sensor_indices:
            data_info = sensor_indices['gyroscope']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_gyroscope(
                    data_info['data'][idx], data_info['noise']
                )
        
        # Magnetometer
        if 'magnetometer' in sensor_indices:
            data_info = sensor_indices['magnetometer']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_magnetometer(
                    data_info['data'][idx], data_info['noise']
                )
        
        # GPS Position
        if 'gps_position' in sensor_indices:
            data_info = sensor_indices['gps_position']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_gps_position(
                    data_info['data'][idx], data_info['noise']
                )
        
        # GPS Velocity
        if 'gps_velocity' in sensor_indices:
            data_info = sensor_indices['gps_velocity']
            idx = self._find_time_index(current_time, data_info['times'], tolerance)
            if idx is not None and not np.any(np.isnan(data_info['data'][idx])):
                self.ins_filter.fuse_gps_velocity(
                    data_info['data'][idx], data_info['noise']
                )
    
    def _find_time_index(self, target_time: float, time_array: np.ndarray, 
                        tolerance: float) -> Optional[int]:
        """Find index of closest time within tolerance"""
        if len(time_array) == 0:
            return None
        
        # Find closest time
        idx = np.argmin(np.abs(time_array - target_time))
        
        # Check if within tolerance
        if np.abs(time_array[idx] - target_time) <= tolerance:
            return idx
        
        return None
'''
    
    with open("fusion/fusion_engine.py", "w") as f:
        f.write(fusion_engine_content)

def generate_analysis_engine():
    """Generate analysis engine"""
    
    analysis_engine_content = '''"""Analysis Engine - Error Analysis and Statistics"""
import numpy as np
import pandas as pd
import h5py
import scipy.io
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from simulation.simulation_engine import SimulationData
from fusion.fusion_engine import EstimationResults

logger = logging.getLogger(__name__)

@dataclass
class AnalysisResults:
    """Complete analysis results"""
    ground_truth: Dict[str, np.ndarray]
    estimates: Dict[str, np.ndarray]
    errors: Dict[str, np.ndarray]
    statistics: Dict[str, float]
    gps_measurements: Optional[Dict[str, np.ndarray]] = None

class AnalysisEngine:
    """Main analysis engine for error computation and statistics"""
    
    def __init__(self):
        pass
    
    def run_analysis(self, simulation_data: SimulationData, 
                    estimation_results: EstimationResults) -> AnalysisResults:
        """
        Run complete error analysis and statistics computation
        
        Args:
            simulation_data: Original simulation data with ground truth
            estimation_results: INS filter estimation results
            
        Returns:
            AnalysisResults containing all analysis data
        """
        logger.info("Starting analysis...")
        
        # Interpolate ground truth to estimation time grid
        ground_truth_interp = self._interpolate_ground_truth(
            simulation_data.ground_truth, estimation_results.time
        )
        
        # Compute errors
        errors = self._compute_errors(ground_truth_interp, estimation_results)
        
        # Compute statistics
        statistics = self._compute_statistics(errors, ground_truth_interp, 
                                            estimation_results, simulation_data)
        
        # Extract GPS measurements for visualization
        gps_measurements = self._extract_gps_measurements(simulation_data)
        
        # Package results
        analysis_results = AnalysisResults(
            ground_truth=ground_truth_interp,
            estimates={
                'time': estimation_results.time,
                'position': estimation_results.position,
                'velocity': estimation_results.velocity,
                'acceleration': estimation_results.acceleration,
                'orientation': estimation_results.orientation,
                'angular_velocity': estimation_results.angular_velocity
            },
            errors=errors,
            statistics=statistics,
            gps_measurements=gps_measurements
        )
        
        logger.info("Analysis completed successfully")
        return analysis_results
    
    def _interpolate_ground_truth(self, ground_truth, target_times: np.ndarray) -> Dict:
        """Interpolate ground truth to target time grid"""
        
        interpolated = {}
        
        # Interpolate position
        interpolated['position'] = np.zeros((len(target_times), 3))
        for i in range(3):
            interpolated['position'][:, i] = np.interp(
                target_times, ground_truth.time, ground_truth.position[:, i]
            )
        
        # Interpolate velocity
        interpolated['velocity'] = np.zeros((len(target_times), 3))
        for i in range(3):
            interpolated['velocity'][:, i] = np.interp(
                target_times, ground_truth.time, ground_truth.velocity[:, i]
            )
        
        # Interpolate acceleration
        interpolated['acceleration'] = np.zeros((len(target_times), 3))
        for i in range(3):
            interpolated['acceleration'][:, i] = np.interp(
                target_times, ground_truth.time, ground_truth.acceleration[:, i]
            )
        
        # Interpolate orientation (quaternions - more complex)
        interpolated['orientation'] = self._interpolate_quaternions(
            ground_truth.time, ground_truth.orientation, target_times
        )
        
        # Interpolate angular velocity
        interpolated['angular_velocity'] = np.zeros((len(target_times), 3))
        for i in range(3):
            interpolated['angular_velocity'][:, i] = np.interp(
                target_times, ground_truth.time, ground_truth.angular_velocity[:, i]
            )
        
        interpolated['time'] = target_times
        
        return interpolated
    
    def _interpolate_quaternions(self, source_times: np.ndarray, 
                               source_quats: np.ndarray, 
                               target_times: np.ndarray) -> np.ndarray:
        """Interpolate quaternions using SLERP (simplified)"""
        from scipy.spatial.transform import Rotation, Slerp
        
        # Create rotation objects
        rotations = Rotation.from_quat(source_quats[:, [1, 2, 3, 0]])  # [x,y,z,w]
        
        # Create SLERP interpolator
        slerp = Slerp(source_times, rotations)
        
        # Interpolate
        interp_rotations = slerp(target_times)
        interp_quats = interp_rotations.as_quat()  # [x,y,z,w]
        
        # Convert back to [w,x,y,z] format
        return interp_quats[:, [3, 0, 1, 2]]
    
    def _compute_errors(self, ground_truth: Dict, 
                       estimation_results: EstimationResults) -> Dict:
        """Compute all error metrics"""
        
        errors = {}
        
        # Position errors
        errors['position'] = estimation_results.position - ground_truth['position']
        
        # Velocity errors
        errors['velocity'] = estimation_results.velocity - ground_truth['velocity']
        
        # Acceleration errors
        errors['acceleration'] = estimation_results.acceleration - ground_truth['acceleration']
        
        # Orientation errors (simplified - using rotation vector difference)
        errors['orientation'] = self._compute_orientation_errors(
            ground_truth['orientation'], estimation_results.orientation
        )
        
        # Angular velocity errors
        errors['angular_velocity'] = estimation_results.angular_velocity - ground_truth['angular_velocity']
        
        return errors
    
    def _compute_orientation_errors(self, true_quats: np.ndarray, 
                                  est_quats: np.ndarray) -> np.ndarray:
        """Compute orientation errors in rotation vector form"""
        from scipy.spatial.transform import Rotation
        
        n_samples = len(true_quats)
        errors = np.zeros(n_samples)
        
        for i in range(n_samples):
            # Convert to rotation objects
            r_true = Rotation.from_quat(true_quats[i, [1, 2, 3, 0]])  # [x,y,z,w]
            r_est = Rotation.from_quat(est_quats[i, [1, 2, 3, 0]])   # [x,y,z,w]
            
            # Relative rotation
            r_error = r_est * r_true.inv()
            
            # Convert to rotation vector magnitude
            rotvec = r_error.as_rotvec()
            errors[i] = np.linalg.norm(rotvec)
        
        return errors
    
    def _compute_statistics(self, errors: Dict, ground_truth: Dict, 
                          estimation_results: EstimationResults, 
                          simulation_data: SimulationData) -> Dict:
        """Compute comprehensive statistics"""
        
        stats = {}
        
        # Position error statistics
        pos_error_2d = np.linalg.norm(errors['position'][:, :2], axis=1)
        stats['rms_position_error_2d'] = np.sqrt(np.mean(pos_error_2d**2))
        stats['max_position_error_2d'] = np.max(pos_error_2d)
        stats['mean_position_error_2d'] = np.mean(pos_error_2d)
        stats['position_error_95th'] = np.percentile(pos_error_2d, 95)
        
        # Altitude error statistics
        alt_error = np.abs(errors['position'][:, 2])
        stats['rms_altitude_error'] = np.sqrt(np.mean(alt_error**2))
        stats['max_altitude_error'] = np.max(alt_error)
        stats['mean_altitude_error'] = np.mean(alt_error)
        
        # Velocity error statistics
        vel_error = np.linalg.norm(errors['velocity'], axis=1)
        stats['rms_velocity_error'] = np.sqrt(np.mean(vel_error**2))
        stats['max_velocity_error'] = np.max(vel_error)
        stats['mean_velocity_error'] = np.mean(vel_error)
        
        # Orientation error statistics
        orient_error_deg = np.degrees(errors['orientation'])
        stats['rms_orientation_error'] = np.sqrt(np.mean(orient_error_deg**2))
        stats['max_orientation_error'] = np.max(orient_error_deg)
        stats['mean_orientation_error'] = np.mean(orient_error_deg)
        
        # Trajectory statistics
        true_pos = ground_truth['position']
        distances = np.linalg.norm(np.diff(true_pos, axis=0), axis=1)
        stats['total_distance'] = np.sum(distances)
        
        true_speeds = np.linalg.norm(ground_truth['velocity'], axis=1)
        stats['average_speed'] = np.mean(true_speeds)
        stats['maximum_speed'] = np.max(true_speeds)
        
        # Simulation parameters
        stats['duration'] = estimation_results.time[-1] - estimation_results.time[0]
        
        # Sensor participation
        sensor_config = simulation_data.configuration['sensors']
        stats['used_accelerometer'] = sensor_config.use_accelerometer
        stats['used_gyroscope'] = sensor_config.use_gyroscope
        stats['used_magnetometer'] = sensor_config.use_magnetometer
        stats['used_gps_position'] = sensor_config.use_gps_position
        stats['used_gps_velocity'] = sensor_config.use_gps_velocity
        
        return stats
    
    def _extract_gps_measurements(self, simulation_data: SimulationData) -> Dict:
        """Extract GPS measurements for visualization"""
        
        sensor_measurements = simulation_data.sensor_measurements
        
        # Convert GPS LLA to NED for visualization
        if len(sensor_measurements.gps_position) > 0:
            gps_lla = sensor_measurements.gps_position
            
            # Remove NaN values
            valid_mask = ~np.any(np.isnan(gps_lla), axis=1)
            if np.any(valid_mask):
                gps_lla_valid = gps_lla[valid_mask]
                
                # Simple LLA to NED conversion (using simulation reference)
                traj_config = simulation_data.configuration['trajectory']
                ref_lat = traj_config.center_lat
                ref_lon = traj_config.center_lon
                ref_alt = traj_config.center_alt
                
                gps_ned = self._lla_to_ned_simple(gps_lla_valid, ref_lat, ref_lon, ref_alt)
                
                return {
                    'position': gps_ned,
                    'time': sensor_measurements.time['gps_position'][valid_mask]
                }
        
        return None
    
    def _lla_to_ned_simple(self, lla: np.ndarray, ref_lat: float, 
                          ref_lon: float, ref_alt: float) -> np.ndarray:
        """Simple LLA to NED conversion"""
        R_earth = 6378137.0  # Earth radius in meters
        
        ned = np.zeros_like(lla)
        
        # Convert to NED
        ned[:, 0] = (lla[:, 0] - ref_lat) * np.pi/180 * R_earth  # North
        ned[:, 1] = (lla[:, 1] - ref_lon) * np.pi/180 * R_earth * np.cos(np.radians(ref_lat))  # East
        ned[:, 2] = -(lla[:, 2] - ref_alt)  # Down
        
        return ned
    
    def export_data(self, simulation_data: SimulationData, filename: str):
        """Export simulation data in various formats"""
        
        file_ext = filename.lower().split('.')[-1]
        
        if file_ext == 'csv':
            self._export_csv(simulation_data, filename)
        elif file_ext == 'mat':
            self._export_mat(simulation_data, filename)
        elif file_ext == 'h5':
            self._export_hdf5(simulation_data, filename)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
        
        logger.info(f"Data exported to: {filename}")
    
    def _export_csv(self, simulation_data: SimulationData, filename: str):
        """Export to CSV format"""
        
        # Create comprehensive DataFrame
        ground_truth = simulation_data.ground_truth
        
        df = pd.DataFrame({
            'time': ground_truth.time,
            'pos_north': ground_truth.position[:, 0],
            'pos_east': ground_truth.position[:, 1],
            'pos_down': ground_truth.position[:, 2],
            'vel_north': ground_truth.velocity[:, 0],
            'vel_east': ground_truth.velocity[:, 1],
            'vel_down': ground_truth.velocity[:, 2],
            'acc_north': ground_truth.acceleration[:, 0],
            'acc_east': ground_truth.acceleration[:, 1],
            'acc_down': ground_truth.acceleration[:, 2],
            'quat_w': ground_truth.orientation[:, 0],
            'quat_x': ground_truth.orientation[:, 1],
            'quat_y': ground_truth.orientation[:, 2],
            'quat_z': ground_truth.orientation[:, 3],
            'omega_x': ground_truth.angular_velocity[:, 0],
            'omega_y': ground_truth.angular_velocity[:, 1],
            'omega_z': ground_truth.angular_velocity[:, 2]
        })
        
        df.to_csv(filename, index=False)
    
    def _export_mat(self, simulation_data: SimulationData, filename: str):
        """Export to MATLAB format"""
        
        ground_truth = simulation_data.ground_truth
        sensor_measurements = simulation_data.sensor_measurements
        
        mat_data = {
            'ground_truth': {
                'time': ground_truth.time,
                'position': ground_truth.position,
                'velocity': ground_truth.velocity,
                'acceleration': ground_truth.acceleration,
                'orientation': ground_truth.orientation,
                'angular_velocity': ground_truth.angular_velocity
            },
            'sensor_measurements': {
                'accelerometer': sensor_measurements.accelerometer,
                'gyroscope': sensor_measurements.gyroscope,
                'magnetometer': sensor_measurements.magnetometer,
                'gps_position': sensor_measurements.gps_position,
                'gps_velocity': sensor_measurements.gps_velocity,
                'time_vectors': sensor_measurements.time
            },
            'configuration': simulation_data.configuration
        }
        
        scipy.io.savemat(filename, mat_data)
    
    def _export_hdf5(self, simulation_data: SimulationData, filename: str):
        """Export to HDF5 format"""
        
        with h5py.File(filename, 'w') as f:
            # Ground truth group
            gt_group = f.create_group('ground_truth')
            gt_group.create_dataset('time', data=simulation_data.ground_truth.time)
            gt_group.create_dataset('position', data=simulation_data.ground_truth.position)
            gt_group.create_dataset('velocity', data=simulation_data.ground_truth.velocity)
            gt_group.create_dataset('acceleration', data=simulation_data.ground_truth.acceleration)
            gt_group.create_dataset('orientation', data=simulation_data.ground_truth.orientation)
            gt_group.create_dataset('angular_velocity', data=simulation_data.ground_truth.angular_velocity)
            
            # Sensor measurements group
            sensor_group = f.create_group('sensor_measurements')
            sensor_group.create_dataset('accelerometer', data=simulation_data.sensor_measurements.accelerometer)
            sensor_group.create_dataset('gyroscope', data=simulation_data.sensor_measurements.gyroscope)
            sensor_group.create_dataset('magnetometer', data=simulation_data.sensor_measurements.magnetometer)
            sensor_group.create_dataset('gps_position', data=simulation_data.sensor_measurements.gps_position)
            sensor_group.create_dataset('gps_velocity', data=simulation_data.sensor_measurements.gps_velocity)
            
            # Time vectors
            time_group = sensor_group.create_group('time_vectors')
            for sensor, times in simulation_data.sensor_measurements.time.items():
                time_group.create_dataset(sensor, data=times)
'''
    
    with open("analysis/analysis_engine.py", "w") as f:
        f.write(analysis_engine_content)

def generate_data_models():
    """Generate data models and utilities"""
    
    data_models_content = '''"""Data Models and Structures"""
import numpy as np
from dataclasses import dataclass
from typing import Dict, Any, Optional, List

# Re-export important data structures for convenience
from simulation.simulation_engine import GroundTruth, SensorMeasurements, SimulationData
from fusion.fusion_engine import EstimationResults
from analysis.analysis_engine import AnalysisResults

__all__ = [
    'GroundTruth',
    'SensorMeasurements', 
    'SimulationData',
    'EstimationResults',
    'AnalysisResults'
]
'''
    
    with open("data/data_models.py", "w") as f:
        f.write(data_models_content)

def generate_utility_modules():
    """Generate utility modules"""
    
    # Constants
    constants_content = '''"""System Constants and Parameters"""

# Earth model constants
EARTH_RADIUS = 6378137.0  # meters (WGS84 equatorial radius)
EARTH_FLATTENING = 1.0 / 298.257223563  # WGS84 flattening
EARTH_ROTATION_RATE = 7.2921159e-5  # rad/s
STANDARD_GRAVITY = 9.80665  # m/s²

# Coordinate system conventions
COORDINATE_FRAMES = {
    'NED': 'North-East-Down',
    'BODY': 'Body-fixed frame',
    'LLA': 'Latitude-Longitude-Altitude'
}

# Sensor quality templates
SENSOR_QUALITY_SPECS = {
    'CONSUMER': {
        'accelerometer_noise': 1.0,  # m/s²
        'gyroscope_noise': 0.1,     # rad/s
        'magnetometer_noise': 2.0,  # µT
        'description': 'Consumer-grade IMU (smartphone quality)'
    },
    'NAVIGATION': {
        'accelerometer_noise': 0.1,  # m/s²
        'gyroscope_noise': 0.01,    # rad/s
        'magnetometer_noise': 0.5,  # µT
        'description': 'Navigation-grade IMU (automotive/marine)'
    },
    'SURVEY': {
        'accelerometer_noise': 0.01,  # m/s²
        'gyroscope_noise': 0.001,    # rad/s
        'magnetometer_noise': 0.1,   # µT
        'description': 'Survey-grade IMU (high-precision applications)'
    }
}

# Environment condition templates
ENVIRONMENT_CONDITIONS = {
    'IDEAL': {
        'gps_horizontal_accuracy': 0.5,  # meters
        'gps_vertical_accuracy': 1.0,    # meters
        'gps_outage_probability': 0.0,   # 0-1
        'multipath_factor': 1.0,         # multiplier
        'description': 'Perfect conditions, no interference'
    },
    'GOOD': {
        'gps_horizontal_accuracy': 2.0,
        'gps_vertical_accuracy': 3.0,
        'gps_outage_probability': 0.01,
        'multipath_factor': 1.2,
        'description': 'Good conditions, minimal interference'
    },
    'MODERATE': {
        'gps_horizontal_accuracy': 5.0,
        'gps_vertical_accuracy': 8.0,
        'gps_outage_probability': 0.05,
        'multipath_factor': 1.5,
        'description': 'Moderate conditions, some interference'
    },
    'POOR': {
        'gps_horizontal_accuracy': 10.0,
        'gps_vertical_accuracy': 15.0,
        'gps_outage_probability': 0.1,
        'multipath_factor': 2.0,
        'description': 'Poor conditions, significant interference'
    },
    'EXTREME': {
        'gps_horizontal_accuracy': 20.0,
        'gps_vertical_accuracy': 30.0,
        'gps_outage_probability': 0.2,
        'multipath_factor': 3.0,
        'description': 'Extreme conditions, heavy interference'
    }
}

# Trajectory type specifications
TRAJECTORY_TYPES = {
    'CIRCULAR': 'Circular trajectory with constant radius',
    'FIGURE8': 'Figure-8 trajectory (lemniscate)',
    'SQUARE': 'Square trajectory with sharp corners',
    'STRAIGHT': 'Straight line trajectory',
    'SPIRAL': 'Spiral trajectory with increasing/decreasing radius',
    'SURVEY_LINES': 'Survey lines (lawnmower pattern)',
    'COASTAL_PATROL': 'Irregular coastal patrol pattern'
}

# Default magnetic field (Boston area)
DEFAULT_MAGNETIC_FIELD_NED = [20.5, -4.2, 51.8]  # µT

# Color scheme for consistent visualization
VISUALIZATION_COLORS = {
    'true_trajectory': 'blue',
    'estimated_trajectory': 'red',
    'gps_measurements': 'green',
    'start_point': 'green',
    'end_point': 'red',
    'error_plot': 'red',
    'background_grid': 'lightgray'
}
'''
    
    with open("utils/constants.py", "w") as f:
        f.write(constants_content)
    
    # Helper functions
    helpers_content = '''"""Helper Functions and Utilities"""
import numpy as np
from typing import Tuple, Union, Optional
from utils.constants import EARTH_RADIUS

def deg_to_rad(degrees: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """Convert degrees to radians"""
    return degrees * np.pi / 180.0

def rad_to_deg(radians: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """Convert radians to degrees"""
    return radians * 180.0 / np.pi

def quaternion_multiply(q1: np.ndarray, q2: np.ndarray) -> np.ndarray:
    """
    Multiply two quaternions [w, x, y, z]
    
    Args:
        q1, q2: Quaternions in [w, x, y, z] format
        
    Returns:
        Product quaternion
    """
    w1, x1, y1, z1 = q1
    w2, x2, y2, z2 = q2
    
    return np.array([
        w1*w2 - x1*x2 - y1*y2 - z1*z2,  # w
        w1*x2 + x1*w2 + y1*z2 - z1*y2,  # x
        w1*y2 - x1*z2 + y1*w2 + z1*x2,  # y
        w1*z2 + x1*y2 - y1*x2 + z1*w2   # z
    ])

def quaternion_conjugate(q: np.ndarray) -> np.ndarray:
    """
    Compute quaternion conjugate [w, -x, -y, -z]
    
    Args:
        q: Quaternion in [w, x, y, z] format
        
    Returns:
        Conjugate quaternion
    """
    return np.array([q[0], -q[1], -q[2], -q[3]])

def quaternion_to_euler(q: np.ndarray) -> Tuple[float, float, float]:
    """
    Convert quaternion to Euler angles (roll, pitch, yaw)
    
    Args:
        q: Quaternion in [w, x, y, z] format
        
    Returns:
        (roll, pitch, yaw) in radians
    """
    w, x, y, z = q
    
    # Roll (x-axis rotation)
    sinr_cosp = 2 * (w*x + y*z)
    cosr_cosp = 1 - 2 * (x*x + y*y)
    roll = np.arctan2(sinr_cosp, cosr_cosp)
    
    # Pitch (y-axis rotation)
    sinp = 2 * (w*y - z*x)
    if np.abs(sinp) >= 1:
        pitch = np.copysign(np.pi/2, sinp)  # Use 90 degrees if out of range
    else:
        pitch = np.arcsin(sinp)
    
    # Yaw (z-axis rotation)
    siny_cosp = 2 * (w*z + x*y)
    cosy_cosp = 1 - 2 * (y*y + z*z)
    yaw = np.arctan2(siny_cosp, cosy_cosp)
    
    return roll, pitch, yaw

def euler_to_quaternion(roll: float, pitch: float, yaw: float) -> np.ndarray:
    """
    Convert Euler angles to quaternion
    
    Args:
        roll, pitch, yaw: Euler angles in radians
        
    Returns:
        Quaternion in [w, x, y, z] format
    """
    cr = np.cos(roll * 0.5)
    sr = np.sin(roll * 0.5)
    cp = np.cos(pitch * 0.5)
    sp = np.sin(pitch * 0.5)
    cy = np.cos(yaw * 0.5)
    sy = np.sin(yaw * 0.5)
    
    w = cr * cp * cy + sr * sp * sy
    x = sr * cp * cy - cr * sp * sy
    y = cr * sp * cy + sr * cp * sy
    z = cr * cp * sy - sr * sp * cy
    
    return np.array([w, x, y, z])

def normalize_angle(angle: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """Normalize angle to [-pi, pi] range"""
    return np.arctan2(np.sin(angle), np.cos(angle))

def compute_distance_2d(pos1: np.ndarray, pos2: np.ndarray) -> float:
    """Compute 2D Euclidean distance between two positions"""
    return np.linalg.norm(pos1[:2] - pos2[:2])

def compute_bearing(pos1: np.ndarray, pos2: np.ndarray) -> float:
    """
    Compute bearing from pos1 to pos2 in NED frame
    
    Args:
        pos1, pos2: Positions in [north, east, down] format
        
    Returns:
        Bearing in radians (0 = north, positive = clockwise)
    """
    delta_north = pos2[0] - pos1[0]
    delta_east = pos2[1] - pos1[1]
    
    return np.arctan2(delta_east, delta_north)

def smooth_trajectory(positions: np.ndarray, window_size: int = 5) -> np.ndarray:
    """
    Apply moving average smoothing to trajectory
    
    Args:
        positions: [N x 3] array of positions
        window_size: Size of smoothing window
        
    Returns:
        Smoothed positions
    """
    if len(positions) < window_size:
        return positions
    
    smoothed = np.copy(positions)
    half_window = window_size // 2
    
    for i in range(half_window, len(positions) - half_window):
        for j in range(3):
            smoothed[i, j] = np.mean(positions[i-half_window:i+half_window+1, j])
    
    return smoothed

def compute_curvature(positions: np.ndarray) -> np.ndarray:
    """
    Compute trajectory curvature at each point
    
    Args:
        positions: [N x 3] array of positions
        
    Returns:
        Curvature values
    """
    if len(positions) < 3:
        return np.zeros(len(positions))
    
    # First and second derivatives
    vel = np.gradient(positions, axis=0)
    acc = np.gradient(vel, axis=0)
    
    # Curvature = |v x a| / |v|^3
    curvature = np.zeros(len(positions))
    
    for i in range(len(positions)):
        v = vel[i]
        a = acc[i]
        
        v_mag = np.linalg.norm(v)
        if v_mag > 1e-6:
            cross_prod = np.cross(v, a)
            curvature[i] = np.linalg.norm(cross_prod) / (v_mag**3)
    
    return curvature

def validate_configuration(config_dict: dict) -> Tuple[bool, str]:
    """
    Validate configuration dictionary
    
    Args:
        config_dict: Configuration dictionary to validate
        
    Returns:
        (is_valid, error_message)
    """
    try:
        # Check required keys
        required_keys = ['trajectory', 'sensors', 'environment']
        for key in required_keys:
            if key not in config_dict:
                return False, f"Missing required configuration key: {key}"
        
        # Validate numeric ranges
        traj = config_dict['trajectory']
        if traj.get('duration', 0) <= 0:
            return False, "Trajectory duration must be positive"
        
        if traj.get('sampling_rate', 0) <= 0:
            return False, "Sampling rate must be positive"
        
        # Validate sensor rates
        sensors = config_dict['sensors']
        sensor_rates = [
            'accelerometer_rate', 'gyroscope_rate', 'magnetometer_rate',
            'gps_position_rate', 'gps_velocity_rate'
        ]
        
        for rate_key in sensor_rates:
            if sensors.get(rate_key, 0) <= 0:
                return False, f"Sensor rate {rate_key} must be positive"
        
        return True, "Configuration is valid"
        
    except Exception as e:
        return False, f"Configuration validation error: {str(e)}"
'''
    
    with open("utils/helpers.py", "w") as f:
        f.write(helpers_content)
    
    # Coordinate transforms
    transforms_content = '''"""Coordinate Transformation Utilities"""
import numpy as np
from typing import Tuple
from utils.constants import EARTH_RADIUS, EARTH_FLATTENING

def lla_to_ned(lla: np.ndarray, ref_lla: Tuple[float, float, float]) -> np.ndarray:
    """
    Convert LLA coordinates to NED frame
    
    Args:
        lla: [N x 3] array of [lat, lon, alt] in degrees and meters
        ref_lla: Reference LLA (lat, lon, alt) in degrees and meters
        
    Returns:
        [N x 3] array of NED positions in meters
    """
    lat_rad = np.radians(lla[:, 0])
    lon_rad = np.radians(lla[:, 1])
    alt = lla[:, 2]
    
    ref_lat_rad = np.radians(ref_lla[0])
    ref_lon_rad = np.radians(ref_lla[1])
    ref_alt = ref_lla[2]
    
    # Earth model parameters
    a = EARTH_RADIUS
    f = EARTH_FLATTENING
    e2 = 2*f - f**2
    
    # Radius of curvature
    N = a / np.sqrt(1 - e2 * np.sin(ref_lat_rad)**2)
    M = a * (1 - e2) / (1 - e2 * np.sin(ref_lat_rad)**2)**(3/2)
    
    # Convert to NED
    dlat = lat_rad - ref_lat_rad
    dlon = lon_rad - ref_lon_rad
    dalt = alt - ref_alt
    
    north = dlat * (M + ref_alt)
    east = dlon * (N + ref_alt) * np.cos(ref_lat_rad)
    down = -dalt
    
    return np.column_stack([north, east, down])

def ned_to_lla(ned: np.ndarray, ref_lla: Tuple[float, float, float]) -> np.ndarray:
    """
    Convert NED coordinates to LLA frame
    
    Args:
        ned: [N x 3] array of NED positions in meters  
        ref_lla: Reference LLA (lat, lon, alt) in degrees and meters
        
    Returns:
        [N x 3] array of [lat, lon, alt] in degrees and meters
    """
    north = ned[:, 0]
    east = ned[:, 1]
    down = ned[:, 2]
    
    ref_lat_rad = np.radians(ref_lla[0])
    ref_lon_rad = np.radians(ref_lla[1])
    ref_alt = ref_lla[2]
    
    # Earth model parameters
    a = EARTH_RADIUS
    f = EARTH_FLATTENING
    e2 = 2*f - f**2
    
    # Radius of curvature
    N = a / np.sqrt(1 - e2 * np.sin(ref_lat_rad)**2)
    M = a * (1 - e2) / (1 - e2 * np.sin(ref_lat_rad)**2)**(3/2)
    
    # Convert to LLA
    dlat = north / (M + ref_alt)
    dlon = east / ((N + ref_alt) * np.cos(ref_lat_rad))
    dalt = -down
    
    lat = np.degrees(ref_lat_rad + dlat)
    lon = np.degrees(ref_lon_rad + dlon)
    alt = ref_alt + dalt
    
    return np.column_stack([lat, lon, alt])

def ecef_to_lla(ecef: np.ndarray) -> np.ndarray:
    """
    Convert ECEF coordinates to LLA
    
    Args:
        ecef: [N x 3] array of ECEF coordinates in meters
        
    Returns:
        [N x 3] array of [lat, lon, alt] in degrees and meters
    """
    x = ecef[:, 0]
    y = ecef[:, 1]
    z = ecef[:, 2]
    
    # Earth model parameters
    a = EARTH_RADIUS
    f = EARTH_FLATTENING
    e2 = 2*f - f**2
    
    # Longitude
    lon = np.arctan2(y, x)
    
    # Latitude and altitude (iterative)
    p = np.sqrt(x**2 + y**2)
    lat = np.arctan2(z, p * (1 - e2))
    
    # Iterate for better accuracy
    for _ in range(3):
        N = a / np.sqrt(1 - e2 * np.sin(lat)**2)
        alt = p / np.cos(lat) - N
        lat = np.arctan2(z, p * (1 - e2 * N / (N + alt)))
    
    # Final altitude calculation
    N = a / np.sqrt(1 - e2 * np.sin(lat)**2)
    alt = p / np.cos(lat) - N
    
    return np.column_stack([np.degrees(lat), np.degrees(lon), alt])

def lla_to_ecef(lla: np.ndarray) -> np.ndarray:
    """
    Convert LLA coordinates to ECEF
    
    Args:
        lla: [N x 3] array of [lat, lon, alt] in degrees and meters
        
    Returns:
        [N x 3] array of ECEF coordinates in meters
    """
    lat_rad = np.radians(lla[:, 0])
    lon_rad = np.radians(lla[:, 1])
    alt = lla[:, 2]
    
    # Earth model parameters
    a = EARTH_RADIUS
    f = EARTH_FLATTENING
    e2 = 2*f - f**2
    
    # Radius of curvature in prime vertical
    N = a / np.sqrt(1 - e2 * np.sin(lat_rad)**2)
    
    # ECEF coordinates
    x = (N + alt) * np.cos(lat_rad) * np.cos(lon_rad)
    y = (N + alt) * np.cos(lat_rad) * np.sin(lon_rad)
    z = (N * (1 - e2) + alt) * np.sin(lat_rad)
    
    return np.column_stack([x, y, z])

def ned_to_body(ned_vector: np.ndarray, quaternion: np.ndarray) -> np.ndarray:
    """
    Transform vector from NED to body frame using quaternion
    
    Args:
        ned_vector: Vector in NED frame
        quaternion: Quaternion [w, x, y, z] representing NED to body rotation
        
    Returns:
        Vector in body frame
    """
    from scipy.spatial.transform import Rotation
    
    # Convert quaternion format for scipy
    r = Rotation.from_quat([quaternion[1], quaternion[2], quaternion[3], quaternion[0]])
    
    return r.apply(ned_vector, inverse=True)

def body_to_ned(body_vector: np.ndarray, quaternion: np.ndarray) -> np.ndarray:
    """
    Transform vector from body to NED frame using quaternion
    
    Args:
        body_vector: Vector in body frame
        quaternion: Quaternion [w, x, y, z] representing NED to body rotation
        
    Returns:
        Vector in NED frame
    """
    from scipy.spatial.transform import Rotation
    
    # Convert quaternion format for scipy
    r = Rotation.from_quat([quaternion[1], quaternion[2], quaternion[3], quaternion[0]])
    
    return r.apply(body_vector)

def compute_local_gravity(latitude: float, altitude: float = 0.0) -> float:
    """
    Compute local gravity magnitude
    
    Args:
        latitude: Latitude in degrees
        altitude: Altitude in meters above sea level
        
    Returns:
        Gravity magnitude in m/s²
    """
    lat_rad = np.radians(latitude)
    
    # WGS84 gravity formula
    g0 = 9.7803253359  # Equatorial gravity
    g1 = 0.0053024     # Latitude correction
    g2 = 0.0000058     # Latitude squared correction
    
    # Sea level gravity
    g_sea = g0 * (1 + g1 * np.sin(lat_rad)**2 - g2 * np.sin(2*lat_rad)**2)
    
    # Altitude correction
    g_alt = g_sea * (1 - 2 * altitude / EARTH_RADIUS)
    
    return g_alt
'''
    
    with open("utils/coordinate_transforms.py", "w") as f:
        f.write(transforms_content)

def generate_configuration_templates():
    """Generate JSON configuration templates"""
    
    # Trajectory templates
    trajectories = {
        "circular": {
            "trajectory_type": "CIRCULAR",
            "duration": 300.0,
            "sampling_rate": 100.0,
            "radius": 100.0,
            "speed": 5.0,
            "center_lat": 42.3601,
            "center_lon": -71.0589,
            "center_alt": 0.0
        },
        "figure8": {
            "trajectory_type": "FIGURE8",
            "duration": 400.0,
            "sampling_rate": 100.0,
            "figure8_width": 200.0,
            "figure8_height": 100.0,
            "speed": 5.0,
            "center_lat": 42.3601,
            "center_lon": -71.0589,
            "center_alt": 0.0
        },
        "square": {
            "trajectory_type": "SQUARE",
            "duration": 320.0,
            "sampling_rate": 100.0,
            "square_size": 200.0,
            "speed": 5.0,
            "center_lat": 42.3601,
            "center_lon": -71.0589,
            "center_alt": 0.0
        },
        "straight": {
            "trajectory_type": "STRAIGHT",
            "duration": 300.0,
            "sampling_rate": 100.0,
            "speed": 5.0,
            "center_lat": 42.3601,
            "center_lon": -71.0589,
            "center_alt": 0.0
        },
        "spiral": {
            "trajectory_type": "SPIRAL",
            "duration": 600.0,
            "sampling_rate": 100.0,
            "radius": 100.0,
            "spiral_turns": 3,
            "spiral_pitch": 50.0,
            "speed": 3.0,
            "center_lat": 42.3601,
            "center_lon": -71.0589,
            "center_alt": 0.0
        },
        "survey_lines": {
            "trajectory_type": "SURVEY_LINES",
            "duration": 800.0,
            "sampling_rate": 100.0,
            "radius": 200.0,
            "survey_spacing": 50.0,
            "survey_lines": 5,
            "speed": 4.0,
            "center_lat": 42.3601,
            "center_lon": -71.0589,
            "center_alt": 0.0
        },
        "coastal_patrol": {
            "trajectory_type": "COASTAL_PATROL",
            "duration": 600.0,
            "sampling_rate": 100.0,
            "radius": 150.0,
            "coastal_waypoints": 8,
            "coastal_irregularity": 0.2,
            "speed": 6.0,
            "center_lat": 42.3601,
            "center_lon": -71.0589,
            "center_alt": 0.0
        }
    }
    
    # Save trajectory templates
    for name, config in trajectories.items():
        with open(f"config/templates/trajectories/{name}.json", "w") as f:
            json.dump(config, f, indent=4)
    
    # Sensor templates
    sensors = {
        "consumer": {
            "imu_quality": "CONSUMER",
            "accelerometer_rate": 100.0,
            "gyroscope_rate": 100.0,
            "magnetometer_rate": 20.0,
            "gps_position_rate": 5.0,
            "gps_velocity_rate": 5.0,
            "use_accelerometer": True,
            "use_gyroscope": True,
            "use_magnetometer": True,
            "use_gps_position": True,
            "use_gps_velocity": True,
            "accelerometer_noise": 1.0,
            "gyroscope_noise": 0.1,
            "magnetometer_noise": 2.0,
            "gps_position_noise": 5.0,
            "gps_velocity_noise": 0.2
        },
        "navigation": {
            "imu_quality": "NAVIGATION",
            "accelerometer_rate": 100.0,
            "gyroscope_rate": 100.0,
            "magnetometer_rate": 20.0,
            "gps_position_rate": 10.0,
            "gps_velocity_rate": 10.0,
            "use_accelerometer": True,
            "use_gyroscope": True,
            "use_magnetometer": True,
            "use_gps_position": True,
            "use_gps_velocity": True,
            "accelerometer_noise": 0.1,
            "gyroscope_noise": 0.01,
            "magnetometer_noise": 0.5,
            "gps_position_noise": 2.0,
            "gps_velocity_noise": 0.1
        },
        "survey": {
            "imu_quality": "SURVEY",
            "accelerometer_rate": 200.0,
            "gyroscope_rate": 200.0,
            "magnetometer_rate": 50.0,
            "gps_position_rate": 20.0,
            "gps_velocity_rate": 20.0,
            "use_accelerometer": True,
            "use_gyroscope": True,
            "use_magnetometer": True,
            "use_gps_position": True,
            "use_gps_velocity": True,
            "accelerometer_noise": 0.01,
            "gyroscope_noise": 0.001,
            "magnetometer_noise": 0.1,
            "gps_position_noise": 0.5,
            "gps_velocity_noise": 0.05
        }
    }
    
    # Save sensor templates
    for name, config in sensors.items():
        with open(f"config/templates/sensors/{name}.json", "w") as f:
            json.dump(config, f, indent=4)
    
    # Environment templates
    environments = {
        "ideal": {
            "condition_template": "IDEAL",
            "gps_horizontal_accuracy": 0.5,
            "gps_vertical_accuracy": 1.0,
            "gps_velocity_accuracy": 0.05,
            "magnetic_declination": -14.0,
            "magnetic_inclination": 70.0,
            "magnetic_field_strength": 50.0,
            "gps_outage_probability": 0.0,
            "multipath_factor": 1.0
        },
        "good": {
            "condition_template": "GOOD",
            "gps_horizontal_accuracy": 2.0,
            "gps_vertical_accuracy": 3.0,
            "gps_velocity_accuracy": 0.1,
            "magnetic_declination": -14.0,
            "magnetic_inclination": 70.0,
            "magnetic_field_strength": 50.0,
            "gps_outage_probability": 0.01,
            "multipath_factor": 1.2
        },
        "moderate": {
            "condition_template": "MODERATE",
            "gps_horizontal_accuracy": 5.0,
            "gps_vertical_accuracy": 8.0,
            "gps_velocity_accuracy": 0.2,
            "magnetic_declination": -14.0,
            "magnetic_inclination": 70.0,
            "magnetic_field_strength": 50.0,
            "gps_outage_probability": 0.05,
            "multipath_factor": 1.5
        },
        "poor": {
            "condition_template": "POOR",
            "gps_horizontal_accuracy": 10.0,
            "gps_vertical_accuracy": 15.0,
            "gps_velocity_accuracy": 0.5,
            "magnetic_declination": -14.0,
            "magnetic_inclination": 70.0,
            "magnetic_field_strength": 50.0,
            "gps_outage_probability": 0.1,
            "multipath_factor": 2.0
        },
        "extreme": {
            "condition_template": "EXTREME",
            "gps_horizontal_accuracy": 20.0,
            "gps_vertical_accuracy": 30.0,
            "gps_velocity_accuracy": 1.0,
            "magnetic_declination": -14.0,
            "magnetic_inclination": 70.0,
            "magnetic_field_strength": 50.0,
            "gps_outage_probability": 0.2,
            "multipath_factor": 3.0
        }
    }
    
    # Save environment templates
    for name, config in environments.items():
        with open(f"config/templates/environments/{name}.json", "w") as f:
            json.dump(config, f, indent=4)
    
    print("Configuration templates generated successfully!")

def generate_init_files():
    """Generate __init__.py files for all modules"""
    
    # Main package __init__.py
    main_init = '''"""
INS/GPS Fusion Testing Platform

A comprehensive testing platform for evaluating INS/GPS fusion algorithms
in boat motion compensation applications.

Main Components:
- Configuration management with JSON templates
- 7 trajectory types for comprehensive testing
- Complete sensor simulation with selective participation
- Integration with 28-state Extended Kalman Filter
- Comprehensive 2D/3D visualization and analysis
- Data export in multiple formats (CSV, MAT, HDF5)

Usage:
    python main.py

Author: Generated by INS/GPS Project Generator
"""

__version__ = "1.0.0"
__author__ = "INS/GPS Testing Platform"

# Import main components for convenience
from gui.main_gui import MainGUI
from config.configuration_manager import ConfigurationManager
from simulation.simulation_engine import SimulationEngine
from fusion.fusion_engine import FusionEngine
from analysis.analysis_engine import AnalysisEngine

__all__ = [
    'MainGUI',
    'ConfigurationManager', 
    'SimulationEngine',
    'FusionEngine',
    'AnalysisEngine'
]
'''
    
    with open("__init__.py", "w") as f:
        f.write(main_init)
    
    # Individual module __init__.py files
    modules = {
        "config": '''"""Configuration Management Module"""
from .configuration_manager import ConfigurationManager, TrajectoryConfig, SensorConfig, EnvironmentConfig

__all__ = ['ConfigurationManager', 'TrajectoryConfig', 'SensorConfig', 'EnvironmentConfig']
''',
        
        "gui": '''"""GUI Module"""
from .main_gui import MainGUI
from .configuration_tab import ConfigurationTab
from .visualization_tab import VisualizationTab

__all__ = ['MainGUI', 'ConfigurationTab', 'VisualizationTab']
''',
        
        "gui/widgets": '''"""GUI Widgets Module"""
# Custom GUI widgets would be imported here
__all__ = []
''',
        
        "simulation": '''"""Simulation Engine Module"""
from .simulation_engine import SimulationEngine, TrajectoryGenerator, SensorSimulator
from .simulation_engine import GroundTruth, SensorMeasurements, SimulationData

__all__ = [
    'SimulationEngine', 'TrajectoryGenerator', 'SensorSimulator',
    'GroundTruth', 'SensorMeasurements', 'SimulationData'
]
''',
        
        "fusion": '''"""Fusion Engine Module"""
from .fusion_engine import FusionEngine, EstimationResults
from .saeid_gps_ins_ekf import ProductionINSFilter, INSFilterConfig

__all__ = ['FusionEngine', 'EstimationResults', 'ProductionINSFilter', 'INSFilterConfig']
''',
        
        "analysis": '''"""Analysis Engine Module"""
from .analysis_engine import AnalysisEngine, AnalysisResults

__all__ = ['AnalysisEngine', 'AnalysisResults']
''',
        
        "data": '''"""Data Models Module"""
from .data_models import *

# All exports are handled by data_models.py
''',
        
        "utils": '''"""Utilities Module"""
from . import constants
from . import helpers
from . import coordinate_transforms

__all__ = ['constants', 'helpers', 'coordinate_transforms']
''',
        
        "tests": '''"""Tests Module"""
# Test modules would be imported here
__all__ = []
'''
    }
    
    # Write all __init__.py files
    for module_path, content in modules.items():
        with open(f"{module_path}/__init__.py", "w") as f:
            f.write(content)
    
    print("Module __init__.py files generated successfully!")

def generate_readme_and_docs():
    """Generate README and documentation files"""
    
    readme_content = '''# INS/GPS Fusion Testing Platform

A comprehensive, production-ready testing platform for evaluating INS/GPS fusion algorithms in boat motion compensation applications.

## Features

### 🎯 Complete Testing Suite
- **7 Trajectory Types**: Circular, Figure-8, Square, Straight, Spiral, Survey Lines, Coastal Patrol
- **3 IMU Quality Levels**: Consumer, Navigation, Survey grade sensors
- **5 Environment Conditions**: From ideal to extreme GPS interference scenarios
- **Selective Sensor Participation**: Enable/disable any sensor during estimation

### 🧮 Advanced Simulation
- **28-State Extended Kalman Filter**: Production-ready INS/GPS fusion
- **Complete Sensor Models**: Accelerometer, gyroscope, magnetometer, GPS position/velocity
- **Realistic Noise Models**: Based on actual sensor specifications
- **Configurable Sampling Rates**: Individual control for each sensor type

### 📊 Comprehensive Analysis
- **2D/3D Trajectory Visualization**: True vs estimated paths with GPS measurements
- **Error Analysis**: Position, velocity, orientation errors with statistics
- **Interactive Plots**: Zoom, pan, rotate with professional visualization
- **Statistical Reports**: RMS, max, mean errors with 95th percentile analysis

### 💾 Data Management
- **JSON Configuration**: Human-readable, version-controlled settings
- **Template System**: Pre-defined scenarios with auto-loading
- **Multiple Export Formats**: CSV, MATLAB, HDF5 for external analysis
- **Industry Standards**: Compatible with common INS/GPS data formats

## Quick Start

### Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**:
   ```bash
   python main.py
   ```

### First Steps

1. **Configure Trajectory**: Choose from 7 trajectory types in Configuration tab
2. **Set Sensor Parameters**: Select IMU quality and individual sensor rates
3. **Choose Environment**: Set GPS accuracy and interference conditions
4. **Run Simulation**: Click "Run Simulation" to process complete scenario
5. **Analyze Results**: Review plots and statistics in Visualization tab

## System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    GUI Layer (Tkinter)                         │
├─────────────────────────────────────────────────────────────────┤
│  Tab 1: Configuration    │  Tab 2: Visualization & Results     │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                 Core Processing Layer                           │
├─────────────────────────────────────────────────────────────────┤
│  Simulation Engine  │  Fusion Engine  │  Analysis Engine       │
│  - Trajectory Gen.  │  - INS Filter   │  - Error Computation   │
│  - Sensor Models    │  - Integration  │  - Statistics          │
│  - Noise Models     │  - Fusion Logic │  - Visualization Data  │
└─────────────────────────────────────────────────────────────────┘
```

## Configuration Examples

### Circular Trajectory - Navigation Grade
```json
{
  "trajectory": {
    "trajectory_type": "CIRCULAR",
    "duration": 300.0,
    "radius": 100.0,
    "speed": 5.0
  },
  "sensors": {
    "imu_quality": "NAVIGATION",
    "accelerometer_rate": 100.0,
    "gps_position_rate": 10.0
  },
  "environment": {
    "condition_template": "GOOD",
    "gps_horizontal_accuracy": 2.0
  }
}
```

### Survey Mission - High Precision
```json
{
  "trajectory": {
    "trajectory_type": "SURVEY_LINES",
    "survey_spacing": 50.0,
    "survey_lines": 5
  },
  "sensors": {
    "imu_quality": "SURVEY",
    "accelerometer_rate": 200.0
  },
  "environment": {
    "condition_template": "IDEAL"
  }
}
```

## Trajectory Types

| Type | Description | Parameters |
|------|-------------|------------|
| **CIRCULAR** | Constant radius circular path | radius, speed |
| **FIGURE8** | Figure-8 pattern (lemniscate) | width, height |
| **SQUARE** | Square with sharp corners | size |
| **STRAIGHT** | Straight line motion | speed, duration |
| **SPIRAL** | Expanding/contracting spiral | turns, pitch |
| **SURVEY_LINES** | Lawnmower survey pattern | spacing, lines |
| **COASTAL_PATROL** | Irregular coastal patrol | waypoints, irregularity |

## Sensor Quality Specifications

| Quality | Accelerometer | Gyroscope | Magnetometer | Applications |
|---------|---------------|-----------|--------------|--------------|
| **Consumer** | 1.0 m/s² | 0.1 rad/s | 2.0 µT | Smartphones, basic navigation |
| **Navigation** | 0.1 m/s² | 0.01 rad/s | 0.5 µT | Automotive, marine systems |
| **Survey** | 0.01 m/s² | 0.001 rad/s | 0.1 µT | High-precision applications |

## Error Analysis Metrics

- **RMS Position Error**: Root-mean-square 2D position accuracy
- **Maximum Position Error**: Worst-case position deviation
- **95th Percentile Error**: Statistical confidence metric
- **Velocity Error**: Speed and direction accuracy
- **Orientation Error**: Heading and attitude accuracy

## File Structure

```
ins_gps_testing_platform/
├── main.py                    # Application entry point
├── requirements.txt           # Python dependencies
├── config/                    # Configuration management
│   ├── configuration_manager.py
│   └── templates/            # JSON templates
├── gui/                      # User interface
│   ├── main_gui.py
│   ├── configuration_tab.py
│   └── visualization_tab.py
├── simulation/               # Trajectory and sensor simulation
│   └── simulation_engine.py
├── fusion/                   # INS filter integration
│   ├── fusion_engine.py
│   └── saeid_gps_ins_ekf.py  # 28-state EKF implementation
├── analysis/                 # Error analysis and statistics
│   └── analysis_engine.py
├── data/                     # Data models and export
│   └── data_models.py
├── utils/                    # Utilities and helpers
│   ├── constants.py
│   ├── helpers.py
│   └── coordinate_transforms.py
└── exports/                  # Generated data and plots
```

## Technical Details

### INS Filter Specifications
- **28-State Extended Kalman Filter**: Complete implementation
- **State Vector**: Position, velocity, acceleration, orientation, angular velocity, sensor biases, magnetic field
- **Measurement Models**: Accelerometer, gyroscope, magnetometer, GPS position/velocity
- **Coordinate Frames**: NED navigation frame, body-fixed frame
- **Geodetic Transforms**: WGS84 ellipsoid model

### Numerical Methods
- **Quaternion Kinematics**: Exact integration using matrix exponential
- **Covariance Propagation**: Joseph form for numerical stability
- **Sensor Fusion**: Selective participation without regenerating data
- **Error Computation**: Proper statistical analysis with confidence intervals

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed via `pip install -r requirements.txt`
2. **GUI Not Responding**: Check that tkinter is available (usually included with Python)
3. **Slow Performance**: Reduce trajectory duration or sampling rate for faster processing
4. **Memory Issues**: Large simulations may require more RAM; consider shorter durations

### Performance Tips

- **Trajectory Duration**: Start with 300s for initial testing
- **Sampling Rates**: 100Hz for IMU, 10Hz for GPS is usually sufficient
- **Sensor Participation**: Disable unused sensors to speed up processing
- **Visualization**: Use "Clear Results" between runs to free memory

## Support and Development

This is a complete, standalone testing platform generated specifically for INS/GPS fusion evaluation. The system is designed to be:

- **Self-contained**: No external dependencies beyond Python packages
- **Configurable**: All parameters adjustable via GUI or JSON files
- **Extensible**: Clean architecture allows easy addition of new features
- **Professional**: Production-ready code with comprehensive error handling

For additional trajectory types, sensor models, or analysis features, the modular architecture supports straightforward extensions.

## License

This software is provided as-is for research and development purposes in marine navigation and motion compensation applications.
'''
    
    with open("README.md", "w") as f:
        f.write(readme_content)
    
    # Generate a simple user guide
    user_guide_content = '''# INS/GPS Testing Platform - User Guide

## Getting Started

### 1. Launch Application
```bash
python main.py
```

### 2. Configuration Tab

#### Trajectory Setup
1. Select trajectory type from dropdown
2. Adjust duration and sampling rate
3. Set trajectory-specific parameters
4. Configure center location (lat/lon)

#### Sensor Configuration
1. Choose IMU quality template
2. Set individual sensor rates
3. Enable/disable sensors for fusion
4. Load sensor templates as needed

#### Environment Settings
1. Select environment condition
2. Adjust GPS accuracy sliders
3. Set environmental parameters

### 3. Run Simulation
1. Click "Run Simulation" button
2. Monitor progress bar
3. Wait for completion message
4. Switch to Visualization tab

### 4. Results Analysis

#### Trajectory Plots
- 2D trajectory comparison
- Position vs time plots
- Speed analysis
- Altitude tracking

#### Error Analysis
- Position error over time
- Error distribution histograms
- Velocity and orientation errors
- Statistical summaries

#### 3D Visualization
- Interactive 3D trajectory
- Vessel orientation display
- GPS measurement points
- Start/end markers

### 5. Data Export
1. Use "Export Data" from File menu
2. Choose format (CSV, MAT, HDF5)
3. Select export location
4. Data saved for external analysis

## Configuration Tips

### For Accurate Results
- Use Survey-grade sensors
- Set environment to Good/Ideal
- Enable all sensors
- Use appropriate sampling rates

### For Challenging Scenarios
- Use Consumer-grade sensors
- Set environment to Poor/Extreme
- Disable some sensors
- Add GPS outages

### For Performance Testing
- Use long trajectories (>600s)
- Complex patterns (Figure-8, Spiral)
- High sampling rates (200Hz+)
- Multiple sensor types

## Troubleshooting

### Simulation Fails
- Check trajectory parameters
- Reduce duration if memory issues
- Verify sensor rate values
- Ensure positive numeric values

### Poor Visualization
- Clear results between runs
- Check for NaN values in data
- Verify coordinate transformations
- Use reasonable plot ranges

### Export Issues
- Check file permissions
- Verify export directory exists
- Ensure sufficient disk space
- Try different export formats
'''
    
    with open("USER_GUIDE.md", "w") as f:
        f.write(user_guide_content)
    
    print("Documentation generated successfully!")

# Complete the main function
if __name__ == "__main__":
    print("🚀 Starting INS/GPS Testing Platform Project Generation...")
    print("=" * 60)
    
    create_project_structure()
    generate_configuration_templates()
    generate_init_files()
    generate_readme_and_docs()
    
    print("=" * 60)
    print("✅ INS/GPS Testing Platform Generated Successfully!")
    print()
    print("📁 Project Structure Created:")
    print("   ├── Complete GUI with 2 tabs")
    print("   ├── 7 trajectory types implemented")
    print("   ├── 3 sensor quality templates")
    print("   ├── 5 environment conditions")
    print("   ├── 28-state INS filter integrated")
    print("   ├── Comprehensive visualization")
    print("   ├── Data export capabilities")
    print("   └── JSON configuration system")
    print()
    print("🎯 Next Steps:")
    print("   1. cd ins_gps_testing_platform")
    print("   2. pip install -r requirements.txt")
    print("   3. python main.py")
    print()
    print("🔧 Features Ready:")
    print("   • Complete GUI with professional layout")
    print("   • All 7 trajectory types functional")
    print("   • Selective sensor fusion capability")
    print("   • 2D/3D visualization with error analysis")
    print("   • Statistical analysis and reporting")
    print("   • Template-based configuration system")
    print("   • CSV/MAT/HDF5 data export")
    print()
    print("🎉 Your Production-Ready INS/GPS Testing Platform is Complete!")
    print("   Ready for boat motion compensation evaluation!")
    
    def _generate_straight(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate straight line trajectory"""
        n_points = len(time)
        
        # Straight line in north direction
        north = config.speed * time
        east = np.zeros(n_points)
        down = np.zeros(n_points)
        
        position = np.column_stack([north, east, down])
        
        # Constant velocity
        velocity = np.zeros((n_points, 3))
        velocity[:, 0] = config.speed  # North velocity
        
        # Zero acceleration
        acceleration = np.zeros((n_points, 3))
        
        # Constant orientation (facing north)
        orientation = np.zeros((n_points, 4))
        orientation[:, 0] = 1.0  # w component of identity quaternion
        
        # Zero angular velocity
        angular_velocity = np.zeros((n_points, 3))
        
        return GroundTruth(time, position, velocity, acceleration, 
                          orientation, angular_velocity)
    
    def _generate_spiral(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate spiral trajectory"""
        n_points = len(time)
        
        # Spiral parameters
        total_angle = config.spiral_turns * 2 * np.pi
        max_radius = config.radius
        angle_rate = total_angle / config.duration
        
        angles = angle_rate * time
        radius = max_radius * angles / total_angle
        
        # Positions
        north = radius * np.cos(angles)
        east = radius * np.sin(angles)
        down = -config.spiral_pitch * angles / (2 * np.pi)  # Descending spiral
        
        position = np.column_stack([north, east, down])
        
        # Numerical derivatives
        dt = time[1] - time[0]
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)
        
        # Orientation and angular velocity
        orientation = self._compute_orientation_from_velocity(velocity)
        angular_velocity = self._compute_angular_velocity(orientation, dt)
        
        return GroundTruth(time, position, velocity, acceleration, 
                          orientation, angular_velocity)
    
    def _generate_survey_lines(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate survey lines (lawnmower pattern)"""
        n_points = len(time)
        
        # Survey area dimensions
        line_length = config.radius * 2  # Use radius as half-width
        spacing = config.survey_spacing
        n_lines = config.survey_lines
        
        # Total pattern time
        line_time = line_length / config.speed
        turn_time = spacing / config.speed
        total_line_time = line_time + turn_time
        
        position = np.zeros((n_points, 3))
        
        for i, t in enumerate(time):
            # Which line are we on?
            line_cycle_time = t % (total_line_time * n_lines)
            line_idx = int(line_cycle_time / total_line_time)
            time_in_line = line_cycle_time % total_line_time
            
            # North position (varies with line)
            north = (line_idx - n_lines/2) * spacing
            
            # East position (back and forth)
            if time_in_line < line_time:
                # Moving along survey line
                if line_idx % 2 == 0:
                    east = -line_length/2 + config.speed * time_in_line
                else:
                    east = line_length/2 - config.speed * time_in_line
            else:
                # Turning to next line
                if line_idx % 2 == 0:
                    east = line_length/2
                else:
                    east = -line_length/2
            
            position[i] = [north, east, 0]
        
        # Numerical derivatives
        dt = time[1] - time[0]
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)
        
        # Smooth near turns
        for j in range(3):
            velocity[:, j] = self._smooth_signal(velocity[:, j], window_size=10)
            acceleration[:, j] = self._smooth_signal(acceleration[:, j], window_size=10)
        
        # Orientation and angular velocity
        orientation = self._compute_orientation_from_velocity(velocity)
        angular_velocity = self._compute_angular_velocity(orientation, dt)
        
        return GroundTruth(time, position, velocity, acceleration, 
                          orientation, angular_velocity)
        
    def _generate_coastal_patrol(self, time: np.ndarray, config: TrajectoryConfig) -> GroundTruth:
        """Generate coastal patrol trajectory"""
        n_points = len(time)
        
        # Create irregular coastal pattern
        n_waypoints = config.coastal_waypoints
        irregularity = config.coastal_irregularity
        
        # Generate waypoints
        angles = np.linspace(0, 2*np.pi, n_waypoints, endpoint=False)
        base_radius = config.radius
        
        waypoints = []
        for angle in angles:
            # Add irregularity
            r = base_radius * (1 + irregularity * (np.random.random() - 0.5))
            north = r * np.cos(angle)
            east = r * np.sin(angle)
            waypoints.append([north, east, 0])
        
        waypoints = np.array(waypoints)
        
        # Interpolate between waypoints
        position = np.zeros((n_points, 3))
        
        # Total perimeter
        perimeter = 0
        for i in range(n_waypoints):
            next_i = (i + 1) % n_waypoints
            perimeter += np.linalg.norm(waypoints[next_i] - waypoints[i])
        
        period = perimeter / config.speed
        t_norm = (time % period) / period
        
        for i, t in enumerate(t_norm):
            # Find which segment we're on
            segment_progress = t * n_waypoints
            segment_idx = int(segment_progress) % n_waypoints
            local_progress = segment_progress - segment_idx
            
            # Interpolate between waypoints
            current_wp = waypoints[segment_idx]
            next_wp = waypoints[(segment_idx + 1) % n_waypoints]
            
            position[i] = current_wp + local_progress * (next_wp - current_wp)
        
        # Numerical derivatives
        dt = time[1] - time[0]
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)
        
        # Smooth near turns
        for j in range(3):
            velocity[:, j] = self._smooth_signal(velocity[:, j], window_size=10)
            acceleration[:, j] = self._smooth_signal(acceleration[:, j], window_size=10)
        
        # Orientation and angular velocity
        orientation = self._compute_orientation_from_velocity(velocity)
        angular_velocity = self._compute_angular_velocity(orientation, dt)
        
        return GroundTruth(time, position, velocity, acceleration, 
                          orientation, angular_velocity)
    
    