#!/usr/bin/env python3
"""
Simple test to verify that imports work correctly from z_tests folder
"""

import sys
from pathlib import Path

# Add parent directory to Python path to find pohang module
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

def test_imports():
    """Test that all required imports work"""
    print("Testing imports from z_tests folder...")
    
    try:
        from pohang.trajectory_comparison import TrajectoryComparator
        print("✅ TrajectoryComparator import successful")
        
        # Test instantiation
        comparator = TrajectoryComparator()
        print("✅ TrajectoryComparator instantiation successful")
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    try:
        import numpy as np
        import matplotlib.pyplot as plt
        print("✅ Required dependencies (numpy, matplotlib) available")
    except ImportError as e:
        print(f"❌ Dependency missing: {e}")
        return False
    
    print("✅ All imports working correctly!")
    return True

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n🎉 The test environment is ready!")
    else:
        print("\n❌ Please fix the import issues before running tests.")
        sys.exit(1)
