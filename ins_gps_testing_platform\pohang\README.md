# Pohang Dataset Module

## Overview
The Pohang module processes real-world GPS/IMU/LiDAR data from the Pohang Canal dataset for boat motion compensation evaluation. It loads sensor data, runs GPS/IMU fusion, and compares results against ground truth baseline trajectories.

## Key Components

### 1. **PohangDatasetLoader** (`pohang_analizor.py`)
- **Purpose**: Loads and processes Pohang dataset files
- **Data Sources**: GPS, AHRS (IMU), Baseline trajectory, LiDAR (optional)
- **Key Functions**:
  - `load_dataset()`: Loads all sensor data from dataset files
  - `get_lgf_reference_location()`: Extracts Local Geodetic Frame origin from GPS data
  - `sync_sensor_data()`: Time-synchronizes all sensor measurements

### 2. **PohangLidarProcessor** (`pohang_integration.py`)
- **Purpose**: Main processing pipeline for Pohang dataset
- **Key Functions**:
  - `run_ins_gps_fusion()`: Executes GPS/IMU fusion estimation
  - `compare_with_baseline()`: <PERSON>mp<PERSON> estimated trajectory with ground truth
  - `visualize_results()`: Generates comprehensive visualization plots

## Dataset Structure
```
pohang_dataset/
├── navigation/
│   ├── gps.txt          # GPS measurements (lat/lon/alt)
│   ├── ahrs.txt         # IMU data (quaternions, gyro, accel)
│   └── baseline.txt     # Ground truth trajectory (x,y,z in local frame)
└── lidar/               # LiDAR point clouds (optional)
```

## Data Flow
1. **Load Dataset** → Parse GPS/IMU/baseline files
2. **Extract LGF Origin** → Use GPS trajectory center as coordinate reference
3. **Sync Data** → Time-align all sensor measurements
4. **Run Fusion** → Execute GPS/IMU Kalman filter estimation
5. **Align Coordinates** → Translate estimated trajectory to baseline frame
6. **Compare & Visualize** → Generate error analysis and trajectory plots

## Coordinate Systems

### Problem Solved
- **Issue**: Estimated trajectory and baseline were in different coordinate frames
- **Solution**: Simple coordinate alignment via translation

### Alignment Method
```python
# Align estimated trajectory to baseline coordinate system
est_center = np.mean(estimated_trajectory, axis=0)
baseline_center = np.mean(baseline_trajectory, axis=0)
aligned_trajectory = estimated_trajectory - est_center + baseline_center
```

### Coordinate Frame
- **X-axis**: North (positive northward)
- **Y-axis**: East (positive eastward)  
- **Z-axis**: Down (positive downward)
- **Origin**: Baseline coordinate system center

## Visualization Outputs

### Generated Files (in `../exports/pohang_results/`)
1. **`estimated_trajectory_only.png`** - Fusion results in original frame
2. **`ground_truth_trajectory_only.png`** - Baseline trajectory 
3. **`comprehensive_trajectory_comparison.png`** - 6-panel comparison (3D, 2D, time series, statistics)
4. **`detailed_error_analysis.png`** - RMS errors, distributions, error mapping
5. **`coordinate_frame_alignment.png`** - Frame alignment verification

### Key Metrics
- **Horizontal RMS Error**: 2D position accuracy
- **Vertical RMS Error**: Altitude accuracy  
- **3D RMS Error**: Total position accuracy
- **Error Statistics**: Mean, max, standard deviation
- **Trajectory Statistics**: Distance, duration, sample counts

## Usage

### From GUI
1. Open "Pohang Dataset" tab
2. Select dataset path: `../../../MOSTAFAVI-02.ffgg.ulaval.ca/Doodman/lidar_gps_ins`
3. Configure options (fusion, baseline comparison, visualization)
4. Click "Run Processing"

### From Code
```python
from pohang.pohang_integration import PohangLidarProcessor

# Initialize processor
processor = PohangLidarProcessor(dataset_path)

# Load dataset
processor.load_dataset()

# Run GPS/IMU fusion
fusion_results = processor.run_ins_gps_fusion()

# Compare with baseline
metrics = processor.compare_with_baseline()

# Generate visualizations
processor.visualize_results(save_plots=True)
```

## Key Features

### ✅ **Coordinate System Consistency**
- All trajectories displayed in same coordinate frame
- Simple translation-based alignment
- Proper coordinate system verification

### ✅ **Comprehensive Error Analysis**
- RMS error calculations
- Error distribution analysis
- Temporal error evolution
- Statistical metrics

### ✅ **Robust Data Loading**
- Handles missing files gracefully
- Time synchronization across sensors
- Hemisphere correction for GPS coordinates

### ✅ **Professional Visualizations**
- High-resolution plots (300 DPI)
- Multiple view perspectives (3D, 2D, time series)
- Detailed coordinate frame information
- Error magnitude color mapping

## Dependencies
- **Core**: `numpy`, `matplotlib`, `scipy`
- **Coordinate Transforms**: `utils.coordinate_transforms`
- **Fusion Engine**: `fusion.fusion_engine`
- **Configuration**: `config.configuration_manager`

## Important Notes

### ⚠️ **Coordinate System**
- Baseline trajectory is ground truth - used as reference frame
- Estimated trajectory is translated to baseline frame for comparison
- LGF origin extracted from GPS data center (not hardcoded coordinates)

### ⚠️ **Data Requirements**
- GPS data must have lat/lon/alt columns with hemisphere indicators
- Baseline data must be in local coordinate system (x,y,z)
- Time synchronization assumes consistent sampling rates

### ⚠️ **Processing Notes**
- Fusion engine runs independently of simulation subsystem
- Baseline used for evaluation only (not initialization)
- All coordinate transformations use WGS84 parameters

## Troubleshooting

### Common Issues
1. **"Coordinate system mismatch"** → Check if alignment is applied correctly
2. **"No baseline data"** → Verify baseline.txt exists and is readable
3. **"GPS parsing error"** → Check GPS file format and hemisphere indicators
4. **"Large RMS errors"** → Verify coordinate alignment is working

### Debug Tools
- `test_coordinate_fixes.py` - Validates coordinate transformations
- `test_simple_alignment.py` - Tests coordinate alignment approach
- Individual trajectory plots show original coordinate ranges
