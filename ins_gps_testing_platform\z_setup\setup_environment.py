#!/usr/bin/env python3
"""
INS/GPS Testing Platform - Environment Setup Script

This script checks the Python environment and installs required dependencies
for the INS/GPS Testing Platform.
For manual installation, use requirements.txt file 


Usage:
    python setup_environment.py
"""

import sys
import subprocess
import pkg_resources
import platform
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def check_python_version():
    """Check Python version compatibility"""
    print_header("PYTHON VERSION CHECK")
    
    version = sys.version_info
    print(f"Python Version: {version.major}.{version.minor}.{version.micro}")
    print(f"Platform: {platform.platform()}")
    print(f"Architecture: {platform.architecture()[0]}")
    
    # Check minimum version requirement (Python 3.7+)
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ ERROR: Python 3.7 or higher is required!")
        print("   Please upgrade your Python installation.")
        return False
    else:
        print("✅ Python version is compatible")
        return True

def get_installed_packages():
    """Get list of currently installed packages"""
    try:
        installed_packages = {pkg.project_name.lower(): pkg.version 
                            for pkg in pkg_resources.working_set}
        return installed_packages
    except Exception as e:
        print(f"Warning: Could not retrieve installed packages: {e}")
        print(f"Make sure haev selected the correct Python environment.")
        return {}

def check_package_availability():
    """Check which required packages are already installed"""
    
    
    # Required packages from requirements.txt
    required_packages = {
        'numpy': '>=1.21.0',
        'scipy': '>=1.7.0', 
        'matplotlib': '>=3.5.0',
        'plotly': '>=5.0.0',
        'pandas': '>=1.3.0',
        'h5py': '>=3.6.0'
    }
    
    # Standard library modules (should always be available)
    standard_modules = ['tkinter', 'pathlib', 'json', 'os', 'sys', 
                       'logging', 'copy', 'dataclasses', 'typing']
    
    installed_packages = get_installed_packages()
    missing_packages = []
    
    print("Required Third-Party Packages:")
    for package, version_req in required_packages.items():
        if package in installed_packages:
            print(f"  ✅ {package}: {installed_packages[package]} (installed)")
        else:
            print(f"  ❌ {package}: {version_req} (missing)")
            missing_packages.append(package)
    
    print("\nStandard Library Modules:")
    for module in standard_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}: Available")
        except ImportError:
            print(f"  ❌ {module}: Not available")
            if module == 'tkinter':
                print("     WARNING: tkinter is required for GUI functionality!")
    
    return missing_packages

def install_requirements():
    """Install missing packages from requirements.txt"""
    print_header("INSTALLING REQUIREMENTS")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ ERROR: requirements.txt file not found!")
        return False
    
    print(f"Installing packages from: {requirements_file}")
    print("This may take a few minutes...")
    
    try:
        # Use pip to install requirements
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], capture_output=True, text=True, check=True)
        
        print("✅ Package installation completed successfully!")
        if result.stdout:
            print("\nInstallation output:")
            print(result.stdout)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ ERROR: Package installation failed!")
        print(f"Error code: {e.returncode}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ ERROR: Unexpected error during installation: {e}")
        return False

def verify_installation():
    """Verify that all packages can be imported successfully"""
    print_header("INSTALLATION VERIFICATION")
    
    test_imports = [
        ('numpy', 'import numpy as np'),
        ('scipy', 'import scipy'),
        ('matplotlib', 'import matplotlib.pyplot as plt'),
        ('plotly', 'import plotly'),
        ('pandas', 'import pandas as pd'),
        ('h5py', 'import h5py'),
        ('tkinter', 'import tkinter as tk')
    ]
    
    all_success = True
    
    for package_name, import_statement in test_imports:
        try:
            exec(import_statement)
            print(f"  ✅ {package_name}: Import successful")
        except ImportError as e:
            print(f"  ❌ {package_name}: Import failed - {e}")
            all_success = False
        except Exception as e:
            print(f"  ⚠️  {package_name}: Import warning - {e}")
    
    return all_success

def main():
    """Main setup function"""
    print_header("INS/GPS TESTING PLATFORM - ENVIRONMENT SETUP")
    print("This script will check your Python environment and install required packages.")
    
    # Step 1: Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Step 2: Check package availability
    missing_packages = check_package_availability()
    
    # Step 3: Install missing packages if any
    if missing_packages:
        print(f"\nFound {len(missing_packages)} missing packages.")
        response = input("Do you want to install missing packages? (y/n): ").lower().strip()
        
        if response in ['y', 'yes']:
            if not install_requirements():
                print("\n❌ Setup failed due to installation errors.")
                sys.exit(1)
        else:
            print("⚠️  Setup incomplete - missing packages not installed.")
            sys.exit(1)
    else:
        print("\n✅ All required packages are already installed!")
    
    # Step 4: Verify installation
    if verify_installation():
        print_header("=================================")
        print("✅ Environment setup completed successfully!")
        print("✅ All required packages are installed and working.")
        print("\nYou can now run the platform with:")
        print("    python main.py")
    else:
        print_header("SETUP ISSUES")
        print("⚠️  Some packages may have installation issues.")
        print("   Please check the error messages above and resolve them manually.")
        sys.exit(1)

if __name__ == "__main__":
    main()
